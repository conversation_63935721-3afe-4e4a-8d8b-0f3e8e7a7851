<?php

return [
    'academic' => 'Academic',
    'student_strength' => 'Student Strength',
    'principal' => 'Principal',
    'vice_principal' => 'Vice Principal',
    'principal_incharge' => 'Principal Incharge',
    'teacher' => 'Teacher',
    'class_teacher' => 'Class Teacher',
    'subject_teacher' => 'Subject Teacher',
    'config' => [
        'config' => 'Config',
        'props' => [
            'allow_listing_subject_wise_student' => 'Allow Listing Subject Wise Student',
        ],
    ],
    'period_selection' => 'Session Selection',
    'period_selection_options' => [
        'period_wise' => 'Session Wise',
        'session_wise' => 'Session Wise',
    ],
    'department' => [
        'academic_department' => 'Department',
        'department' => 'Department',
        'departments' => 'Departments',
        'incharge' => 'Heads of Department',
        'module_title' => 'List all Department',
        'module_description' => 'Departments are divisions within an educational institution responsible for specific subject areas or fields of study. For example, the Mathematics Department, Science Department, or Humanities Department.',
        'props' => [
            'name' => 'Name',
            'code' => 'Code',
            'shortcode' => 'Short Code',
            'alias' => 'Alias',
            'description' => 'Description',
        ],
    ],
    'program' => [
        'program' => 'Program',
        'programs' => 'Programs',
        'incharge' => 'Heads of Program',
        'module_title' => 'List all Program',
        'module_description' => 'Programs are structured set of courses leading to a degree, diploma, or certification in a specific field. For example, Bachelor of Science, Diploma in Business Administration, etc.',
        'registration_disabled_info' => 'Registration is disabled for this program.',
        'types' => [
            'k12' => 'K12',
            'diploma' => 'Diploma',
            'under_graduate' => 'Under Graduate',
            'post_graduate' => 'Post Graduate',
            'research' => 'Research',
            'pre-primary_education' => 'Pre-Primary Education', 
            'primary_education' => 'Primary Education', 
            'junior_secondary_education' => 'Junior Secondary Education', 
            'senior_secondary_education' => 'Senior Secondary Education'
        ],
        'props' => [
            'name' => 'Name',
            'code' => 'Code',
            'shortcode' => 'Short Code',
            'alias' => 'Alias',
            'type' => 'Type',
            'description' => 'Description',
        ],
    ],
    'session' => [
        'session' => 'Session',
        'sessions' => 'Sessions',
        'module_title' => 'List all Sessions',
        'module_description' => 'Sessions are academic years or periods of time that represent a specific academic calendar.',
        'props' => [
            'name' => 'Name',
            'code' => 'Code',
            'shortcode' => 'Short Code',
            'alias' => 'Alias',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'description' => 'Description',
        ],
    ],
    'period' => [
        'period' => 'Session',
        'periods' => 'Sessions',
        'current_period' => 'Current Session',
        'default_period' => 'Default Session',
        'default' => 'Default',
        'set_as_default' => 'Set as Default',
        'module_title' => 'List all Sessions',
        'module_description' => 'Sessions are years or period of time that represents a specific calendar.',
        'could_not_import_from_different_program' => 'Could not import from different program.',
        'registration_disabled_info' => 'Registration is disabled for this Period/Session.',
        //'session_info' => 'Session is optional. Create a session only if your session contains multiple terms.',
        'session_info' => '',
        'props' => [
            'name' => 'Name',
            'code' => 'Code',
            'shortcode' => 'Short Code',
            'alias' => 'Alias',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'description' => 'Description',
        ],
    ],
    'division' => [
        'division' => 'Division',
        'divisions' => 'Divisions',
        'module_title' => 'List all Division',
        'incharge' => 'Heads of Division',
        'module_description' => 'Divisions are groups of courses that are taught by staff.',
        'props' => [
            'name' => 'Name',
            'alias' => 'Alias',
            'code' => 'Code',
            'shortcode' => 'Short Code',
            'description' => 'Description',
            'holiday_exceptions' => 'Holiday Exceptions',
        ],
    ],
    'course' => [
        'course' => 'Course',
        'courses' => 'Courses',
        'incharge' => 'Heads of Course',
        'module_title' => 'List all Course',
        'module_description' => 'Courses are specific areas of study or disciplines within a program.',
        'registration' => 'Registration',
        'registration_fee_info' => 'Registration fee for selected course is :attribute.',
        'registration_disabled_info' => 'Registration is disabled for this course.',
        'batch_with_same_subject_info' => 'All the batches of this course have same subjects',
        'could_not_modify_same_subject_field_after_assigning_batch_wise_subject' => 'Could not modify same subject field after assigning batch arm wise subject.',
        'props' => [
            'name' => 'Name',
            'term' => 'Term',
            'description' => 'Description',
            'title' => 'Course Title',
            'code' => 'Course Code',
            'shortcode' => 'Short Code',
            'enable_registration' => 'Enable Registration',
            'registration_fee' => 'Registration Fee',
            'batch_with_same_subject' => 'Batch With Same Subject',
            'holiday_exceptions' => 'Holiday Exceptions',
        ],
    ],
    'batch' => [
        'batch' => 'Batch',
        'batches' => 'Batches',
        'module_title' => 'List all Batches',
        'incharge' => 'Heads of Batch',
        'module_description' => 'Batches are groups of students who are enrolled in the same class in same course and period/session. For example, JSS1A, SSS2 Art, etc.',
        'current_strength' => 'Current Strength :attribute',
        'props' => [
            'name' => 'Name',
            'max_strength' => 'Max Strength',
            'roll_number_prefix' => 'Roll Number Prefix',
            'description' => 'Description',
            'holiday_exceptions' => 'Holiday Exceptions',
        ],
    ],
    'subject' => [
        'subject' => 'Subject',
        'subjects' => 'Subjects',
        'incharge' => 'Subject Teachers',
        'module_title' => 'List all Subject',
        'module_description' => 'Subjects are specific areas of study or disciplines within a program.',
        'allocation' => 'Subject Allocation',
        'already_allocated' => ':attribute has already subject allocated.',
        'is_not_an_elective_subject' => 'Selected subject is not an elective subject.',
        'record' => 'Subject Record',
        'additional_subject' => 'Additional Subject',
        'elective_subject' => 'Elective Subject',
        'props' => [
            'name' => 'Name',
            'alias' => 'Alias',
            'code' => 'Code',
            'shortcode' => 'Short Code',
            'position' => 'Position',
            'credit' => 'Credit',
            'type' => 'Type',
            'max_class_per_week' => 'Max Class Per Week',
            'fee' => 'Subject Fee',
            'exam_fee' => 'Exam Fee',
            'course_fee' => 'Course Fee',
            'is_elective' => 'Is Elective',
            'has_no_exam' => 'Has No Exam',
            'has_grading' => 'Has Grading',
            'description' => 'Description',
        ],
        'type' => [
            'type' => 'Subject Type',
            'types' => 'Subject Types',
            'module_title' => 'Manage all Subject Types',
            'module_description' => 'Subject Types are categories that define the characteristics of a subject. For example, Theory, Practical, etc.',
            'props' => [
                'name' => 'Name',
                'description' => 'Description',
            ],
        ],
    ],
    'book_list' => [
        'book_list' => 'Book list',
        'book_lists' => 'Book lists',
        'module_title' => 'List all Book list',
        'module_description' => 'Manage all Book list',
        'types' => [
            'textbook' => 'Textbook',
            'reference_book' => 'Reference Book',
            'notebook' => 'Notebook',
        ],
        'props' => [
            'title' => 'Title',
            'author' => 'Author',
            'publisher' => 'Publisher',
            'type' => 'Type',
            'quantity' => 'Quantity',
            'pages' => 'Pages',
            'description' => 'Description',
        ],
    ],
    'department_incharge' => [
        'department_incharge' => 'Head of Department',
        'department_incharges' => 'Heads of Department',
        'module_title' => 'List all Heads of Department',
        'module_description' => 'Manage all Heads of Department',
    ],
    'program_incharge' => [
        'program_incharge' => 'Head of Program',
        'program_incharges' => 'Heads of Program',
        'module_title' => 'List all Heads of Program',
        'module_description' => 'Manage all Heads of Program',
    ],
    'division_incharge' => [
        'division_incharge' => 'Head of Division',
        'division_incharges' => 'Heads of Division',
        'module_title' => 'List all Heads of Division',
        'module_description' => 'Manage all Heads of Division',
    ],
    'course_incharge' => [
        'course_incharge' => 'Course Teacher',
        'course_incharges' => 'Course Teachers',
        'module_title' => 'List all Course Teachers',
        'module_description' => 'Manage all Course Teachers',
    ],
    'batch_incharge' => [
        'batch_incharge' => 'Batch Teacher',
        'batch_incharges' => 'Batch Teachers',
        'module_title' => 'List all Batch Teachers',
        'module_description' => 'Manage all Batch Teachers',
    ],
    'subject_incharge' => [
        'subject_incharge' => 'Subject Teacher',
        'subject_incharges' => 'Subject Teachers',
        'module_title' => 'List all Subject Teachers',
        'module_description' => 'Manage all Subject Teachers',
    ],
    'certificate' => [
        'type' => 'Type',
        'types' => [
            'transfer_certificate' => 'Transfer Certificate',
            'other' => 'Other',
        ],
        'for' => [
            'student' => 'Student',
            'employee' => 'Employee',
        ],
        'template' => [
            'template' => 'Certificate Template',
            'templates' => 'Certificate Templates',
            'module_title' => 'List all Certificate Templates',
            'module_description' => 'Manage all Certificate Templates',
            'props' => [
                'name' => 'Name',
                'type' => 'Type',
                'for' => 'For',
                'custom_field' => 'Custom Field',
                'content' => 'Content',
                'custom_template_file' => 'Custom Template File',
                'has_custom_template_file' => 'Has Custom Template File',
                'has_custom_header' => 'Has Custom Header',
                'custom_template_file_name' => 'Custom Template File Name',
                'number_prefix' => 'Certificate Number Prefix',
                'number_digit' => 'Certificate Number Digit',
                'number_suffix' => 'Certificate Number Suffix',
            ],
        ],
        'certificate' => 'Certificate',
        'certificates' => 'Certificates',
        'module_title' => 'List all Certificates',
        'module_description' => 'Manage all Certificates',
        'already_exists' => 'Certificate already exists for the given details.',
        'props' => [
            'code_number' => 'Certificate #',
            'date' => 'Date',
            'to' => 'To',
            'show_label' => 'Show Label',
            'hide_label' => 'Hide Label',
        ],
        'search_placeholder' => 'Search field:value,another_field:another_value',
    ],
    'id_card' => [
        'id_card' => 'ID Card',
        'id_cards' => 'ID Cards',
        'module_title' => 'List all ID Cards',
        'module_description' => 'Manage all ID Cards',
        'card_per_page' => 'Card Per Page',
        'for' => [
            'student' => 'Student',
            'employee' => 'Employee',
            'guardian' => 'Guardian',
        ],
        'template' => [
            'template' => 'ID Card Template',
            'templates' => 'ID Card Templates',
            'module_title' => 'List all ID Card Templates',
            'module_description' => 'Manage all ID Card Templates',
            'props' => [
                'name' => 'Name',
                'for' => 'For',
                'custom_template_file' => 'Custom Template File',
                'has_custom_template_file' => 'Has Custom Template File',
                'custom_template_file_name' => 'Custom Template File Name',
            ],
        ],
    ],
    'class_timing' => [
        'class_timing' => 'Class Timing',
        'class_timings' => 'Class Timings',
        'module_title' => 'List all Class Timings',
        'module_description' => 'Manage all Class Timings',
        'session' => 'Session',
        'break' => 'Break',
        'start_time_should_less_than_end_time' => 'Start time should be less than end time.',
        'start_time_should_greater_than_previous_session_end_time' => 'Start time should be greater than previous session end time.',
        'could_not_modify_if_allocated' => 'Could not modify if timetable is allocated.',
        'props' => [
            'name' => 'Session Name',
            'is_break' => 'Is Break',
            'start_time' => 'Start Time',
            'end_time' => 'End Time',
            'session' => 'Session',
            'duration' => 'Duration',
            'description' => 'Description',
        ],
    ],
    'timetable' => [
        'timetable' => 'Timetable',
        'timetables' => 'Timetables',
        'allotment' => 'Allotment',
        'teacher_timetable' => 'Teacher Timetable',
        'duplicate_allotment' => 'Duplicate subject in same session.',
        'module_title' => 'List all Timetables',
        'module_description' => 'Manage all Timetables',
        'invalid_days' => 'All days of the week should be available.',
        'invalid_day' => 'Invalid day of the week.',
        'all_days_should_be_filled' => 'All days should be filled.',
        'could_not_modify_if_allocated' => 'Could not modify if subjects are allocated.',
        'timetable_not_found_for_today' => 'Timetable not defined for today.',
        'holiday_info' => ':attribute is a holiday.',
        'class_timing_not_found' => 'Could not found class timing.',
        'employee_not_assigned_to_subject' => 'Employee is not assigned to this subject.',
        'allocation' => 'Allocation',
        'props' => [
            'day' => 'Day',
            'holiday' => 'Holiday',
            'effective_date' => 'Effective Date',
            'description' => 'Description',
        ],
    ],
];
