<?php

namespace App\Services\Quiz;

use App\Models\Quiz\Quiz;
use App\Models\Quiz\QuizAttempt;
use App\Models\Quiz\QuizQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class QuizAttemptService
{
    public function preRequisite(Request $request): array
    {
        return [];
    }

    public function findByUuidOrFail(string $uuid): QuizAttempt
    {
        return QuizAttempt::query()
            ->where('uuid', $uuid)
            ->getOrFail(trans('quiz.attempt'), 'message');
    }

    public function startAttempt(Request $request, Quiz $quiz): QuizAttempt
    {
        // Check if user already has an in-progress attempt
        $existingAttempt = QuizAttempt::query()
            ->where('quiz_id', $quiz->id)
            ->where('user_id', auth()->id())
            ->where('status', 'in_progress')
            ->first();

        if ($existingAttempt) {
            return $existingAttempt;
        }

        // Check attempt limits if configured
        $this->validateAttemptLimits($quiz);

        \DB::beginTransaction();

        $attempt = QuizAttempt::forceCreate([
            'quiz_id' => $quiz->id,
            'user_id' => auth()->id(),
            'participant_name' => $request->participant_name,
            'participant_email' => $request->participant_email,
            'status' => 'in_progress',
            'started_at' => now(),
            'answers' => [],
            'meta' => [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ],
        ]);

        \DB::commit();

        return $attempt;
    }

    public function saveAnswer(Request $request, QuizAttempt $attempt): void
    {
        if ($attempt->status !== 'in_progress') {
            throw ValidationException::withMessages(['message' => trans('quiz.attempt.cannot_save_answer_completed_attempt')]);
        }

        \DB::beginTransaction();

        $answers = $attempt->answers ?? [];
        $answers[$request->question_id] = $request->answer;

        $attempt->forceFill([
            'answers' => $answers,
        ])->save();

        \DB::commit();
    }

    public function submitAttempt(Request $request, QuizAttempt $attempt): void
    {
        if ($attempt->status !== 'in_progress') {
            throw ValidationException::withMessages(['message' => trans('quiz.attempt.cannot_submit_completed_attempt')]);
        }

        \DB::beginTransaction();

        // Calculate score
        $scoreData = $this->calculateScore($attempt);

        $attempt->forceFill([
            'status' => 'completed',
            'completed_at' => now(),
            'submitted_at' => now(),
            'score' => $scoreData['score'],
            'percentage' => $scoreData['percentage'],
            'meta' => array_merge($attempt->meta ?? [], [
                'total_questions' => $scoreData['total_questions'],
                'correct_answers' => $scoreData['correct_answers'],
                'incorrect_answers' => $scoreData['incorrect_answers'],
                'unanswered' => $scoreData['unanswered'],
            ]),
        ])->save();

        \DB::commit();
    }

    public function autoSubmitAttempt(QuizAttempt $attempt): void
    {
        if ($attempt->status !== 'in_progress') {
            return;
        }

        \DB::beginTransaction();

        // Calculate score
        $scoreData = $this->calculateScore($attempt);

        $attempt->forceFill([
            'status' => 'auto_submitted',
            'completed_at' => now(),
            'submitted_at' => now(),
            'score' => $scoreData['score'],
            'percentage' => $scoreData['percentage'],
            'meta' => array_merge($attempt->meta ?? [], [
                'total_questions' => $scoreData['total_questions'],
                'correct_answers' => $scoreData['correct_answers'],
                'incorrect_answers' => $scoreData['incorrect_answers'],
                'unanswered' => $scoreData['unanswered'],
                'auto_submitted' => true,
            ]),
        ])->save();

        \DB::commit();
    }

    public function abandonAttempt(QuizAttempt $attempt): void
    {
        if ($attempt->status !== 'in_progress') {
            throw ValidationException::withMessages(['message' => trans('quiz.attempt.cannot_abandon_completed_attempt')]);
        }

        \DB::beginTransaction();

        $attempt->forceFill([
            'status' => 'abandoned',
            'completed_at' => now(),
        ])->save();

        \DB::commit();
    }

    private function validateAttemptLimits(Quiz $quiz): void
    {
        $config = $quiz->config ?? [];
        $maxAttempts = Arr::get($config, 'max_attempts');

        if ($maxAttempts && $maxAttempts > 0) {
            $userAttempts = QuizAttempt::query()
                ->where('quiz_id', $quiz->id)
                ->where('user_id', auth()->id())
                ->whereIn('status', ['completed', 'auto_submitted'])
                ->count();

            if ($userAttempts >= $maxAttempts) {
                throw ValidationException::withMessages(['message' => trans('quiz.attempt.max_attempts_reached')]);
            }
        }
    }

    private function calculateScore(QuizAttempt $attempt): array
    {
        $quiz = $attempt->quiz;
        $questions = $quiz->questions;
        $answers = $attempt->answers ?? [];

        $totalQuestions = $questions->count();
        $correctAnswers = 0;
        $totalPoints = 0;
        $earnedPoints = 0;

        foreach ($questions as $question) {
            $totalPoints += $question->points;
            $userAnswer = $answers[$question->id] ?? null;

            if ($this->isAnswerCorrect($question, $userAnswer)) {
                $correctAnswers++;
                $earnedPoints += $question->points;
            }
        }

        $percentage = $totalPoints > 0 ? ($earnedPoints / $totalPoints) * 100 : 0;

        return [
            'score' => $earnedPoints,
            'percentage' => round($percentage, 2),
            'total_questions' => $totalQuestions,
            'correct_answers' => $correctAnswers,
            'incorrect_answers' => $totalQuestions - $correctAnswers - (count($answers) < $totalQuestions ? $totalQuestions - count($answers) : 0),
            'unanswered' => $totalQuestions - count($answers),
        ];
    }

    private function isAnswerCorrect(QuizQuestion $question, $userAnswer): bool
    {
        if ($userAnswer === null) {
            return false;
        }

        $correctAnswers = $question->correct_answers ?? [];

        switch ($question->question_type) {
            case 'mcq':
                return in_array($userAnswer, $correctAnswers);

            case 'true_false':
                return in_array($userAnswer, $correctAnswers);

            case 'single_line':
                // For single line, we can implement fuzzy matching or exact matching
                foreach ($correctAnswers as $correct) {
                    if (strtolower(trim($userAnswer)) === strtolower(trim($correct))) {
                        return true;
                    }
                }
                return false;

            case 'multi_line':
                // For multi-line, we can implement keyword matching or manual grading
                // For now, return false as these typically require manual grading
                return false;

            default:
                return false;
        }
    }
}
