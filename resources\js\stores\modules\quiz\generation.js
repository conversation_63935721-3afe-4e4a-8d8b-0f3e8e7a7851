import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import { useToast } from "vue-toastification"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/quiz/generate",
    formErrors: {},
    isGenerating: false,
    recentGenerations: [],
    prerequisites: null,
})

const generation = {
    namespaced: true,
    state: initialState,
    mutations: {
        ...mutations,
        SET_GENERATING(state, status) {
            state.isGenerating = status
        },
        SET_RECENT_GENERATIONS(state, generations) {
            state.recentGenerations = generations
        },
        SET_PREREQUISITES(state, prerequisites) {
            state.prerequisites = prerequisites
        },
        ADD_RECENT_GENERATION(state, generation) {
            state.recentGenerations.unshift(generation)
            // Keep only the last 10 generations
            if (state.recentGenerations.length > 10) {
                state.recentGenerations = state.recentGenerations.slice(0, 10)
            }
        },
    },
    actions: {
        ...actions,
        async getPreRequisite({ commit, state }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/pre-requisite`)
                commit("SET_PREREQUISITES", response)
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async generate({ commit, state }, { endpoint, data }) {
            try {
                commit("SET_GENERATING", true)
                commit("SET_FORM_ERRORS", {})
                
                // Prepare form data
                const formData = new FormData()
                
                // Add all form fields
                Object.keys(data).forEach(key => {
                    if (data[key] !== null && data[key] !== undefined) {
                        if (Array.isArray(data[key])) {
                            data[key].forEach((item, index) => {
                                formData.append(`${key}[${index}]`, item)
                            })
                        } else if (data[key] instanceof File) {
                            formData.append(key, data[key])
                        } else {
                            formData.append(key, data[key])
                        }
                    }
                })
                
                const response = await Api.post(`${state.initURL}/${endpoint}`, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                })
                
                commit("SET_GENERATING", false)
                commit("ADD_RECENT_GENERATION", response.quiz)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_GENERATING", false)
                if (error.response?.data?.errors) {
                    commit("SET_FORM_ERRORS", error.response.data.errors)
                }
                Form.handleErrors(error)
                throw error
            }
        },
        async generateFromText({ dispatch }, data) {
            return dispatch('generate', { endpoint: 'text', data })
        },
        async generateFromTopic({ dispatch }, data) {
            return dispatch('generate', { endpoint: 'topic', data })
        },
        async generateFromUrl({ dispatch }, data) {
            return dispatch('generate', { endpoint: 'url', data })
        },
        async generateFromDocument({ dispatch }, data) {
            return dispatch('generate', { endpoint: 'document', data })
        },
        async getRecentGenerations({ commit, state }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/recent`)
                commit("SET_RECENT_GENERATIONS", response.generations || [])
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getGenerationHistory({ commit, state }, params = {}) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/history`, { params })
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async regenerateQuiz({ commit, state }, { uuid, ...params }) {
            try {
                commit("SET_GENERATING", true)
                const response = await Api.post(`${state.initURL}/${uuid}/regenerate`, params)
                commit("SET_GENERATING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_GENERATING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async saveGeneratedQuiz({ commit, state }, { conversationId, ...params }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(`${state.initURL}/save`, {
                    conversationId,
                    ...params,
                })
                commit("SET_LOADING", false)
                commit("ADD_RECENT_GENERATION", response.quiz)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async validateContent({ commit, state }, { type, content }) {
            try {
                const response = await Api.post(`${state.initURL}/validate`, {
                    type,
                    content,
                })
                return response
            } catch (error) {
                Form.handleErrors(error)
                throw error
            }
        },
        async extractContentFromUrl({ commit, state }, url) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(`${state.initURL}/extract-url`, { url })
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async extractContentFromDocument({ commit, state }, document) {
            try {
                commit("SET_LOADING", true)
                const formData = new FormData()
                formData.append('document', document)
                
                const response = await Api.post(`${state.initURL}/extract-document`, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                })
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
    },
    getters: {
        ...getters,
        isGenerating: (state) => state.isGenerating,
        recentGenerations: (state) => state.recentGenerations,
        prerequisites: (state) => state.prerequisites,
        formErrors: (state) => state.formErrors,
    },
}

export default generation
