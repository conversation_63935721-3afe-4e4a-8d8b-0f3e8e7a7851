<?php

namespace App\Policies\Quiz;

use App\Models\Quiz\Quiz;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class QuizPolicy
{
    use HandlesAuthorization;

    private function validateTeam(User $user, Quiz $quiz)
    {
        return $quiz->team_id == $user->current_team_id;
    }

    private function validatePeriod(User $user, Quiz $quiz)
    {
        return $quiz->period_id == $user->current_period_id;
    }

    /**
     * Determine whether the user can request for pre-requisites.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function preRequisite(User $user)
    {
        return $user->canAny(['quiz:create', 'quiz:edit']);
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('quiz:read');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Quiz $quiz)
    {
        if (!$this->validateTeam($user, $quiz)) {
            return false;
        }

        if (!$this->validatePeriod($user, $quiz)) {
            return false;
        }

        if (!$user->can('quiz:read')) {
            return false;
        }

        // Students can view published quizzes or their own personal quizzes
        if ($user->hasAnyRole(['student'])) {
            return $quiz->status === 'published' || 
                   ($quiz->user_id === $user->id && $quiz->quiz_type === 'personal');
        }

        // Teachers can view all quizzes in their team/period
        return true;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('quiz:create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Quiz $quiz)
    {
        if (!$this->validateTeam($user, $quiz)) {
            return false;
        }

        if (!$this->validatePeriod($user, $quiz)) {
            return false;
        }

        if (!$user->can('quiz:edit')) {
            return false;
        }

        // Students can only edit their own personal quizzes
        if ($user->hasAnyRole(['student'])) {
            return $quiz->user_id === $user->id && $quiz->quiz_type === 'personal';
        }

        // Teachers and admins can edit quizzes they created or have permission for
        return $quiz->user_id === $user->id || $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Quiz $quiz)
    {
        if (!$this->validateTeam($user, $quiz)) {
            return false;
        }

        if (!$this->validatePeriod($user, $quiz)) {
            return false;
        }

        if (!$user->can('quiz:delete')) {
            return false;
        }

        // Students can only delete their own personal quizzes
        if ($user->hasAnyRole(['student'])) {
            return $quiz->user_id === $user->id && $quiz->quiz_type === 'personal';
        }

        // Teachers and admins can delete quizzes they created or have permission for
        return $quiz->user_id === $user->id || $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can publish the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function publish(User $user, Quiz $quiz)
    {
        if (!$this->validateTeam($user, $quiz)) {
            return false;
        }

        if (!$this->validatePeriod($user, $quiz)) {
            return false;
        }

        if (!$user->can('quiz:publish')) {
            return false;
        }

        // Students can publish their own personal quizzes for sharing
        if ($user->hasAnyRole(['student'])) {
            return $quiz->user_id === $user->id && $quiz->quiz_type === 'personal';
        }

        // Teachers and admins can publish quizzes they created or have permission for
        return $quiz->user_id === $user->id || $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can duplicate the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function duplicate(User $user, Quiz $quiz)
    {
        if (!$this->validateTeam($user, $quiz)) {
            return false;
        }

        if (!$this->validatePeriod($user, $quiz)) {
            return false;
        }

        if (!$user->can('quiz:create')) {
            return false;
        }

        // Users can duplicate published quizzes or their own quizzes
        return $quiz->status === 'published' || $quiz->user_id === $user->id;
    }

    /**
     * Determine whether the user can take the quiz.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function attempt(User $user, Quiz $quiz)
    {
        if (!$this->validateTeam($user, $quiz)) {
            return false;
        }

        if (!$this->validatePeriod($user, $quiz)) {
            return false;
        }

        if (!$user->can('quiz:attempt')) {
            return false;
        }

        // Quiz must be published to be attempted
        if ($quiz->status !== 'published') {
            return false;
        }

        // Students can attempt published quizzes
        if ($user->hasAnyRole(['student'])) {
            return true;
        }

        // Teachers can attempt quizzes for testing purposes
        return $user->hasAnyRole(['teacher', 'admin', 'super-admin']);
    }

    /**
     * Determine whether the user can view quiz results.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewResults(User $user, Quiz $quiz)
    {
        if (!$this->validateTeam($user, $quiz)) {
            return false;
        }

        if (!$this->validatePeriod($user, $quiz)) {
            return false;
        }

        if (!$user->can('quiz:view-results')) {
            return false;
        }

        // Students can view their own results
        if ($user->hasAnyRole(['student'])) {
            return $quiz->attempts()->where('user_id', $user->id)->exists();
        }

        // Teachers and admins can view results for quizzes they created or have permission for
        return $quiz->user_id === $user->id || $user->hasAnyRole(['admin', 'super-admin']);
    }
}
