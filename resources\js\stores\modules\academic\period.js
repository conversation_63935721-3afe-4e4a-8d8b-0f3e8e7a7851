import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import { useToast } from "vue-toastification"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/academic/periods",
    formErrors: {},
})

const period = {
    namespaced: true,
    state: initialState,
    modules: {},
    mutations: {
        ...mutations,
    },
    actions: {
        resetFormErrors: actions.resetFormErrors,
        list: actions.list,
        preRequisite: actions.preRequisite,
        get: actions.get,
        delete: actions.delete,
        async create({ state, commit, dispatch }, payload) {
            await Api.store(state.initURL, payload.form)
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })

            await dispatch("config/get", false, { root: true })
            await dispatch("auth/user/fetch", false, { root: true })
        },
        async select({ state, commit }, payload) {
            await Api.custom({
                url: state.initURL + "/" + payload.uuid + "/select",
                method: "POST",
                data: null,
            })
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
        async default({ state, commit }, payload) {
            await Api.custom({
                url: state.initURL + "/" + payload.uuid + "/default",
                method: "POST",
                data: null,
            })
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
        async update({ state, commit, dispatch }, payload) {
            await Api.update(state.initURL, payload.uuid, payload.form)
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })

            await dispatch("config/get", false, { root: true })
            await dispatch("auth/user/fetch", false, { root: true })
        },
        async import({ state, commit, dispatch }, payload) {
            await Api.custom({
                url: state.initURL + "/" + payload.uuid + "/import",
                method: "POST",
                data: payload.form,
            })
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
    },
    getters: {
        ...getters,
    },
}

export default period
