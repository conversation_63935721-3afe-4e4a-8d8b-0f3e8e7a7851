export default [
    {
        path: "shared/:code",
        name: "QuizPublicAccess",
        meta: {
            trans: "quiz.sharing.public_access",
            label: "quiz.quiz",
            isNotNav: true,
            belongsTo: "guest",
        },
        component: () => import("@views/Pages/Quiz/Public/Access.vue"),
    },
    {
        path: "shared/:code/register",
        name: "QuizPublicRegister",
        meta: {
            trans: "quiz.participant.register",
            label: "quiz.quiz",
            isNotNav: true,
            belongsTo: "guest",
        },
        component: () => import("@views/Pages/Quiz/Public/Register.vue"),
    },
    {
        path: "shared/:code/take",
        name: "QuizPublicTake",
        meta: {
            trans: "quiz.attempt.start_quiz",
            label: "quiz.quiz",
            isNotNav: true,
            belongsTo: "guest",
        },
        component: () => import("@views/Pages/Quiz/Public/Take.vue"),
    },
    {
        path: "shared/:code/result/:attemptUuid",
        name: "QuizPublicResult",
        meta: {
            trans: "quiz.attempt.view_result",
            label: "quiz.quiz",
            isNotNav: true,
            belongsTo: "guest",
        },
        component: () => import("@views/Pages/Quiz/Public/Result.vue"),
    },
]
