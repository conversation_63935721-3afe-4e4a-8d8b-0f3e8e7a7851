<?php

namespace App\Policies\Quiz;

use App\Models\Quiz\QuizShare;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class QuizSharePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can request for pre-requisites.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function preRequisite(User $user)
    {
        return $user->can('quiz:share');
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('quiz:view-shares');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, QuizShare $share)
    {
        if (!$user->can('quiz:view-shares')) {
            return false;
        }

        // Users can view shares they created or shares directed to them
        if ($share->shared_by_user_id === $user->id || $share->shared_with_user_id === $user->id) {
            return true;
        }

        // Users can view public shares within their team
        if ($share->visibility === 'public' && $share->quiz && $share->quiz->team_id === $user->current_team_id) {
            return true;
        }

        // Users can view team shares within their team
        if ($share->visibility === 'team' && $share->quiz && $share->quiz->team_id === $user->current_team_id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models (share quiz/result).
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('quiz:share');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, QuizShare $share)
    {
        if (!$user->can('quiz:edit-share')) {
            return false;
        }

        // Only the user who created the share can update it
        return $share->shared_by_user_id === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, QuizShare $share)
    {
        if (!$user->can('quiz:delete-share')) {
            return false;
        }

        // Only the user who created the share can delete it
        return $share->shared_by_user_id === $user->id;
    }

    /**
     * Determine whether the user can share a quiz.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function shareQuiz(User $user, $quiz)
    {
        if (!$user->can('quiz:share')) {
            return false;
        }

        // Check if quiz allows peer sharing
        if (!$quiz->allow_peer_sharing) {
            return false;
        }

        // Students can share published quizzes or their own personal quizzes
        if ($user->hasAnyRole(['student'])) {
            return $quiz->status === 'published' || 
                   ($quiz->user_id === $user->id && $quiz->quiz_type === 'personal');
        }

        // Teachers and admins can share quizzes they have access to
        return $quiz->team_id === $user->current_team_id;
    }

    /**
     * Determine whether the user can share a quiz result.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function shareResult(User $user, $attempt)
    {
        if (!$user->can('quiz:share-result')) {
            return false;
        }

        // Check if quiz allows result sharing
        if (!$attempt->quiz->allow_result_sharing) {
            return false;
        }

        // Users can only share their own completed attempts
        return $attempt->user_id === $user->id && $attempt->status === 'completed';
    }
}
