<template>
    <FilterAction
        :init-url="initUrl"
        :form="form"
        @refresh="$emit('refresh')"
        @hide="$emit('hide')"
    >
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div>
                <BaseSelect
                    name="categoryUuid"
                    :label="$trans('quiz.props.category')"
                    v-model="form.categoryUuid"
                    :options="preRequisites.categories"
                    clearable
                />
            </div>
            
            <div>
                <BaseSelect
                    name="difficulty"
                    :label="$trans('quiz.props.difficulty')"
                    v-model="form.difficulty"
                    :options="preRequisites.difficulties"
                    clearable
                />
            </div>
            
            <div>
                <BaseSelect
                    name="isPublic"
                    :label="$trans('quiz.props.visibility')"
                    v-model="form.isPublic"
                    :options="[
                        { label: $trans('quiz.public'), value: true },
                        { label: $trans('quiz.private'), value: false }
                    ]"
                    clearable
                />
            </div>
            
            <div>
                <BaseInput
                    name="createdAtStart"
                    type="date"
                    :label="$trans('global.created_from')"
                    v-model="form.createdAtStart"
                />
            </div>
            
            <div>
                <BaseInput
                    name="createdAtEnd"
                    type="date"
                    :label="$trans('global.created_to')"
                    v-model="form.createdAtEnd"
                />
            </div>
            
            <div>
                <BaseSelect
                    name="hasQuestions"
                    :label="$trans('quiz.has_questions')"
                    v-model="form.hasQuestions"
                    :options="[
                        { label: $trans('quiz.with_questions'), value: true },
                        { label: $trans('quiz.without_questions'), value: false }
                    ]"
                    clearable
                />
            </div>
        </div>
    </FilterAction>
</template>

<script>
export default {
    name: "QuizFilterForm",
}
</script>

<script setup>
import { reactive, onMounted } from "vue"
import { useStore } from "vuex"

const store = useStore()

const initUrl = "quiz/"
const preRequisites = reactive({})

const form = reactive({
    categoryUuid: "",
    difficulty: "",
    isPublic: "",
    createdAtStart: "",
    createdAtEnd: "",
    hasQuestions: "",
})

const fetchPreRequisites = async () => {
    try {
        // TODO: Implement API call to fetch filter prerequisites
        console.log('Fetching filter prerequisites')
    } catch (error) {
        console.error('Error fetching prerequisites:', error)
    }
}

onMounted(() => {
    fetchPreRequisites()
})

defineEmits(['refresh', 'hide'])
</script>
