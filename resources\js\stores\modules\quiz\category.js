import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import { useToast } from "vue-toastification"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/quiz/categories",
    formErrors: {},
    categories: [],
})

const category = {
    namespaced: true,
    state: initialState,
    mutations: {
        ...mutations,
        SET_CATEGORIES(state, categories) {
            state.categories = categories
        },
        ADD_CATEGORY(state, category) {
            state.categories.unshift(category)
        },
        UPDATE_CATEGORY(state, updatedCategory) {
            const index = state.categories.findIndex(cat => cat.uuid === updatedCategory.uuid)
            if (index !== -1) {
                state.categories.splice(index, 1, updatedCategory)
            }
        },
        REMOVE_CATEGORY(state, uuid) {
            state.categories = state.categories.filter(cat => cat.uuid !== uuid)
        },
    },
    actions: {
        ...actions,
        async fetch({ commit, state }, params = {}) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(state.initURL, { params })
                commit("SET_CATEGORIES", response.categories || response.data || [])
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async create({ commit, state }, params) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(state.initURL, params)
                commit("ADD_CATEGORY", response.category)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async update({ commit, state }, { uuid, ...params }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.patch(`${state.initURL}/${uuid}`, params)
                commit("UPDATE_CATEGORY", response.category)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async delete({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.delete(`${state.initURL}/${uuid}`)
                commit("REMOVE_CATEGORY", uuid)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
    },
    getters: {
        ...getters,
        categories: (state) => state.categories,
        getOptions: (state) => () => {
            return state.categories.map(category => ({
                value: category.uuid,
                label: category.name,
            }))
        },
        getCategoryByUuid: (state) => (uuid) => {
            return state.categories.find(category => category.uuid === uuid)
        },
        activeCategoriesCount: (state) => {
            return state.categories.filter(category => category.isActive).length
        },
    },
}

export default category
