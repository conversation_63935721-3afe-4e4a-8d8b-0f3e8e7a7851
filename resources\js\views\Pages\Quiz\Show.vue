<template>
    <PageHeader
        :title="
            $trans(route.meta.trans, {
                attribute: $trans(route.meta.label),
            })
        "
        :navs="[
            {
                label: $trans('quiz.quiz'),
                path: 'QuizList',
            },
        ]"
    >
        <PageHeaderAction
            name="Quiz"
            :title="$trans('quiz.quiz')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <ShowItem
            :init-url="initUrl"
            :uuid="route.params.uuid"
            @setItem="setItem"
            @redirectTo="router.push({ name: 'QuizList' })"
        >
            <BaseCard v-if="quiz.uuid">
                <template #title>
                    {{ quiz.title }}
                </template>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                    <BaseDataView :label="$trans('quiz.props.title')">
                        {{ quiz.title }}
                    </BaseDataView>

                    <BaseDataView :label="$trans('quiz.props.category')">
                        {{ quiz.category?.name || '-' }}
                    </BaseDataView>

                    <BaseDataView 
                        class="col-span-1 sm:col-span-2"
                        :label="$trans('quiz.props.description')"
                    >
                        {{ quiz.description || '-' }}
                    </BaseDataView>

                    <BaseDataView :label="$trans('quiz.props.questions_count')">
                        {{ quiz.questionsCount || 0 }}
                    </BaseDataView>

                    <BaseDataView :label="$trans('quiz.props.attempts_count')">
                        {{ quiz.attemptsCount || 0 }}
                    </BaseDataView>

                    <BaseDataView :label="$trans('quiz.props.time_limit')">
                        {{ quiz.timeLimit ? `${quiz.timeLimit} ${$trans('global.minutes')}` : $trans('quiz.no_time_limit') }}
                    </BaseDataView>

                    <BaseDataView :label="$trans('quiz.props.max_attempts')">
                        {{ quiz.maxAttempts || $trans('quiz.unlimited_attempts') }}
                    </BaseDataView>

                    <BaseDataView :label="$trans('quiz.props.is_public')">
                        <BaseBadge :design="quiz.isPublic ? 'success' : 'secondary'">
                            {{ quiz.isPublic ? $trans('global.yes') : $trans('global.no') }}
                        </BaseBadge>
                    </BaseDataView>

                    <BaseDataView :label="$trans('quiz.props.share_code')" v-if="quiz.shareCode">
                        <code class="bg-gray-100 px-2 py-1 rounded">{{ quiz.shareCode }}</code>
                    </BaseDataView>

                    <BaseDataView :label="$trans('global.created_at')">
                        {{ quiz.createdAt.formatted }}
                    </BaseDataView>

                    <BaseDataView :label="$trans('global.updated_at')">
                        {{ quiz.updatedAt.formatted }}
                    </BaseDataView>
                </dl>

                <div class="mt-6 flex gap-3">
                    <BaseButton
                        design="info"
                        @click="router.push({ name: 'QuizPreview', params: { uuid: quiz.uuid } })"
                    >
                        <i class="fas fa-play mr-2"></i>
                        {{ $trans('quiz.actions.preview') }}
                    </BaseButton>
                    
                    <BaseButton
                        design="secondary"
                        @click="router.push({ name: 'QuizResults', params: { uuid: quiz.uuid } })"
                    >
                        <i class="fas fa-chart-bar mr-2"></i>
                        {{ $trans('quiz.actions.view_results') }}
                    </BaseButton>
                    
                    <BaseButton
                        design="secondary"
                        @click="router.push({ name: 'QuizAnalytics', params: { uuid: quiz.uuid } })"
                    >
                        <i class="fas fa-analytics mr-2"></i>
                        {{ $trans('quiz.actions.view_analytics') }}
                    </BaseButton>
                </div>
            </BaseCard>
        </ShowItem>
    </ParentTransition>
</template>

<script>
export default {
    name: "QuizShow",
}
</script>

<script setup>
import { reactive } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

const initUrl = "quiz/"
const quiz = reactive({})

const setItem = (data) => {
    Object.assign(quiz, data)
}
</script>
