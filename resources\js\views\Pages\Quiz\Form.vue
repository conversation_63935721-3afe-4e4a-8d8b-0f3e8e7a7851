<template>
    <FormAction
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        :init-url="initUrl"
        :uuid="route.params.uuid"
        action="action"
        :init-form="initForm"
        :form="form"
        :after-submit="afterSubmit"
    >
        <div class="grid grid-cols-1 gap-6">
            <!-- Basic Information -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.basic_information') }}
                </template>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="lg:col-span-2">
                        <BaseInput
                            name="title"
                            :label="$trans('quiz.props.title')"
                            v-model="form.title"
                            v-model:error="formErrors.title"
                            required
                        />
                    </div>
                    
                    <div>
                        <BaseSelect
                            name="categoryUuid"
                            :label="$trans('quiz.props.category')"
                            v-model="form.categoryUuid"
                            :options="preRequisites.categories"
                            v-model:error="formErrors.categoryUuid"
                        />
                    </div>
                    
                    <div>
                        <BaseSelect
                            name="difficulty"
                            :label="$trans('quiz.props.difficulty')"
                            v-model="form.difficulty"
                            :options="preRequisites.difficulties"
                            v-model:error="formErrors.difficulty"
                        />
                    </div>
                    
                    <div class="lg:col-span-2">
                        <BaseTextarea
                            name="description"
                            :label="$trans('quiz.props.description')"
                            v-model="form.description"
                            v-model:error="formErrors.description"
                            rows="3"
                        />
                    </div>
                </div>
            </BaseCard>

            <!-- Quiz Settings -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.settings') }}
                </template>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <BaseInput
                            name="timeLimit"
                            type="number"
                            :label="$trans('quiz.props.time_limit_minutes')"
                            v-model="form.timeLimit"
                            v-model:error="formErrors.timeLimit"
                            min="0"
                            :placeholder="$trans('quiz.no_time_limit')"
                        />
                    </div>
                    
                    <div>
                        <BaseInput
                            name="maxAttempts"
                            type="number"
                            :label="$trans('quiz.props.max_attempts')"
                            v-model="form.maxAttempts"
                            v-model:error="formErrors.maxAttempts"
                            min="0"
                            :placeholder="$trans('quiz.unlimited_attempts')"
                        />
                    </div>
                    
                    <div>
                        <BaseInput
                            name="passingScore"
                            type="number"
                            :label="$trans('quiz.props.passing_score_percentage')"
                            v-model="form.passingScore"
                            v-model:error="formErrors.passingScore"
                            min="0"
                            max="100"
                        />
                    </div>
                    
                    <div>
                        <BaseSelect
                            name="questionOrder"
                            :label="$trans('quiz.props.question_order')"
                            v-model="form.questionOrder"
                            :options="preRequisites.questionOrders"
                            v-model:error="formErrors.questionOrder"
                        />
                    </div>
                </div>
            </BaseCard>

            <!-- Display Settings -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.display_settings') }}
                </template>
                
                <div class="space-y-4">
                    <BaseCheckbox
                        name="showResults"
                        :label="$trans('quiz.props.show_results_after_submission')"
                        v-model="form.showResults"
                        v-model:error="formErrors.showResults"
                    />
                    
                    <BaseCheckbox
                        name="showCorrectAnswers"
                        :label="$trans('quiz.props.show_correct_answers')"
                        v-model="form.showCorrectAnswers"
                        v-model:error="formErrors.showCorrectAnswers"
                    />
                    
                    <BaseCheckbox
                        name="allowReview"
                        :label="$trans('quiz.props.allow_review_before_submit')"
                        v-model="form.allowReview"
                        v-model:error="formErrors.allowReview"
                    />
                    
                    <BaseCheckbox
                        name="randomizeQuestions"
                        :label="$trans('quiz.props.randomize_questions')"
                        v-model="form.randomizeQuestions"
                        v-model:error="formErrors.randomizeQuestions"
                    />
                    
                    <BaseCheckbox
                        name="randomizeOptions"
                        :label="$trans('quiz.props.randomize_options')"
                        v-model="form.randomizeOptions"
                        v-model:error="formErrors.randomizeOptions"
                    />
                </div>
            </BaseCard>

            <!-- Access Settings -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.access_settings') }}
                </template>
                
                <div class="space-y-4">
                    <BaseCheckbox
                        name="isPublic"
                        :label="$trans('quiz.props.is_public')"
                        v-model="form.isPublic"
                        v-model:error="formErrors.isPublic"
                    />
                    
                    <BaseCheckbox
                        name="requireRegistration"
                        :label="$trans('quiz.props.require_registration')"
                        v-model="form.requireRegistration"
                        v-model:error="formErrors.requireRegistration"
                    />
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <BaseInput
                                name="startDate"
                                type="datetime-local"
                                :label="$trans('quiz.props.start_date')"
                                v-model="form.startDate"
                                v-model:error="formErrors.startDate"
                            />
                        </div>
                        
                        <div>
                            <BaseInput
                                name="endDate"
                                type="datetime-local"
                                :label="$trans('quiz.props.end_date')"
                                v-model="form.endDate"
                                v-model:error="formErrors.endDate"
                            />
                        </div>
                    </div>
                </div>
            </BaseCard>
        </div>
    </FormAction>
</template>

<script>
export default {
    name: "QuizForm",
}
</script>

<script setup>
import { reactive } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"

const route = useRoute()
const router = useRouter()
const store = useStore()

const initUrl = "quiz/"
const preRequisites = reactive({})
const formErrors = reactive({})

const initForm = reactive({
    title: "",
    description: "",
    categoryUuid: "",
    difficulty: "",
    timeLimit: null,
    maxAttempts: null,
    passingScore: 60,
    questionOrder: "sequential",
    showResults: true,
    showCorrectAnswers: false,
    allowReview: true,
    randomizeQuestions: false,
    randomizeOptions: false,
    isPublic: false,
    requireRegistration: false,
    startDate: "",
    endDate: "",
})

const form = reactive({ ...initForm })

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
}

const afterSubmit = () => {
    router.push({ name: "QuizList" })
}
</script>
