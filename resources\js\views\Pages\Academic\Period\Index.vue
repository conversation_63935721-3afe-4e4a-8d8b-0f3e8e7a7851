<template>
    <ListItem :init-url="initUrl" @setItems="setItems">
        <template #header>
            <PageHeader
                :title="$trans('academic.period.period')"
                :navs="[
                    { label: $trans('academic.academic'), path: 'Academic' },
                ]"
            >
                <PageHeaderAction
                    url="academic/periods/"
                    name="AcademicPeriod"
                    :title="$trans('academic.period.period')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                    config-path="AcademicConfig"
                >
                    <!-- <BaseButton
                        v-if="perform('session:manage')"
                        design="white"
                        @click="router.push({ name: 'AcademicSession' })"
                    >
                        {{ $trans("academic.session.session") }}
                    </BaseButton> -->
                </PageHeaderAction>
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <FilterForm
                    @refresh="emitter.emit('listItems')"
                    @hide="showFilter = false"
                ></FilterForm>
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="periods.headers"
                :meta="periods.meta"
                module="academic.period"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="period in periods.data"
                    :key="period.uuid"
                    @double-click="
                        router.push({
                            name: 'AcademicPeriodShow',
                            params: { uuid: period.uuid },
                        })
                    "
                >
                    <DataCell name="name">
                        {{ period.name }}
                        <BaseBadge
                            v-if="period.isDefault"
                            design="success"
                            class="ml-2"
                        >
                            {{ $trans("academic.period.default") }}
                        </BaseBadge>
                        <TextMuted block>{{ period.alias }}</TextMuted>
                    </DataCell>
                    <DataCell>
                        <i
                            class="far fa-lg fa-check-circle text-success"
                            v-if="period.enableRegistration"
                        ></i>
                    </DataCell>
                    <DataCell name="session">
                        {{ period.session?.name || "-" }}
                    </DataCell>
                    <DataCell name="code">
                        {{ period.code }}
                        <TextMuted block>{{ period.shortcode }}</TextMuted>
                    </DataCell>
                    <DataCell name="startDate">
                        {{ period.startDate.formatted }}
                    </DataCell>
                    <DataCell name="endDate">
                        {{ period.endDate.formatted }}
                    </DataCell>
                    <DataCell name="createdAt">
                        {{ period.createdAt.formatted }}
                    </DataCell>
                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                icon="fas fa-arrow-circle-right"
                                @click="
                                    router.push({
                                        name: 'AcademicPeriodShow',
                                        params: { uuid: period.uuid },
                                    })
                                "
                                >{{ $trans("general.show") }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('period:edit')"
                                icon="fas fa-edit"
                                @click="
                                    router.push({
                                        name: 'AcademicPeriodEdit',
                                        params: { uuid: period.uuid },
                                    })
                                "
                                >{{ $trans("general.edit") }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('period:create')"
                                icon="fas fa-copy"
                                @click="
                                    router.push({
                                        name: 'AcademicPeriodDuplicate',
                                        params: { uuid: period.uuid },
                                    })
                                "
                                >{{
                                    $trans("general.duplicate")
                                }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('period:update') && !period.isDefault"
                                icon="fas fa-star"
                                @click="setAsDefault(period)"
                                >{{
                                    $trans("academic.period.set_as_default")
                                }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('period:delete')"
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: period.uuid,
                                    })
                                "
                                >{{
                                    $trans("general.delete")
                                }}</FloatingMenuItem
                            >
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
                <template #actionButton>
                    <BaseButton
                        v-if="perform('period:create')"
                        @click="router.push({ name: 'AcademicPeriodCreate' })"
                    >
                        {{
                            $trans("global.add", {
                                attribute: $trans("academic.period.period"),
                            })
                        }}
                    </BaseButton>
                </template>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "AcademicPeriodList",
}
</script>

<script setup>
import { ref, reactive, inject } from "vue"
import { useRouter } from "vue-router"
import { perform } from "@core/helpers/action"
import FilterForm from "./Filter.vue"

const router = useRouter()

const emitter = inject("emitter")

let userActions = ["filter"]
if (perform("period:create")) {
    userActions.unshift("create")
}

if (perform("academic:config")) {
    userActions.push("config")
}

let dropdownActions = []
if (perform("period:export")) {
    dropdownActions = ["print", "pdf", "excel"]
}

const initUrl = "academic/period/"
const showFilter = ref(false)

const periods = reactive({})

const setItems = (data) => {
    Object.assign(periods, data)
}

const setAsDefault = async (period) => {
    emitter.emit("actionItem", {
        uuid: period.uuid,
        action: "default",
        confirmation: true,
    })
}
</script>
