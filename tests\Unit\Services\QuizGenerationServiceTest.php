<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\AI\QuizGenerationService;
use App\Services\AI\AIService;
use App\Services\AI\ConversationService;
use App\Services\Quiz\QuizService;
use App\Models\AI\Conversation;
use App\Models\Quiz\Quiz;
use Illuminate\Http\Request;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class QuizGenerationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $quizGenerationService;
    protected $aiService;
    protected $conversationService;
    protected $quizService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the dependencies
        $this->aiService = Mockery::mock(AIService::class);
        $this->conversationService = Mockery::mock(ConversationService::class);
        $this->quizService = Mockery::mock(QuizService::class);

        $this->quizGenerationService = new QuizGenerationService(
            $this->aiService,
            $this->conversationService,
            $this->quizService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_generate_quiz_from_text_content()
    {
        // Arrange
        $request = new Request([
            'content' => 'This is a sample text content about photosynthesis in plants. Photosynthesis is the process by which plants convert light energy into chemical energy.',
            'title' => 'Photosynthesis Quiz',
            'difficulty' => 'intermediate',
            'question_count' => 5,
            'question_types' => ['mcq', 'true_false'],
        ]);

        $mockConversation = Mockery::mock(Conversation::class);
        $mockConversation->uuid = 'test-conversation-uuid';

        $mockQuiz = Mockery::mock(Quiz::class);
        $mockQuiz->uuid = 'test-quiz-uuid';
        $mockQuiz->title = 'Photosynthesis Quiz';

        // Mock conversation service
        $this->conversationService
            ->shouldReceive('findOrCreateConversation')
            ->once()
            ->with('academic_chat', 'quiz_generation_text', Mockery::any())
            ->andReturn($mockConversation);

        // Mock AI service response
        $mockAIResponse = Mockery::mock();
        $mockAIResponse->content = json_encode([
            'title' => 'Photosynthesis Quiz',
            'questions' => [
                [
                    'question' => 'What is photosynthesis?',
                    'type' => 'mcq',
                    'options' => ['A process', 'A plant', 'A chemical', 'A light'],
                    'correct_answer' => 'A process',
                    'explanation' => 'Photosynthesis is the process by which plants convert light energy.',
                    'points' => 2,
                ],
            ],
        ]);

        $this->aiService
            ->shouldReceive('sendMessage')
            ->once()
            ->andReturn($mockAIResponse);

        // Mock quiz service
        $this->quizService
            ->shouldReceive('create')
            ->once()
            ->andReturn($mockQuiz);

        // Act
        $result = $this->quizGenerationService->generateFromText($request);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('conversation_id', $result);
        $this->assertArrayHasKey('quiz', $result);
        $this->assertArrayHasKey('ai_response', $result);
        $this->assertArrayHasKey('source_type', $result);
        $this->assertEquals('test-conversation-uuid', $result['conversation_id']);
        $this->assertEquals('text', $result['source_type']);
    }

    /** @test */
    public function it_can_generate_quiz_from_topic()
    {
        // Arrange
        $request = new Request([
            'topic' => 'Photosynthesis',
            'subject' => 'Biology',
            'level' => 'Grade 10',
            'difficulty' => 'intermediate',
            'question_count' => 10,
            'question_types' => ['mcq', 'short_answer'],
        ]);

        $mockConversation = Mockery::mock(Conversation::class);
        $mockConversation->uuid = 'test-conversation-uuid';

        $mockQuiz = Mockery::mock(Quiz::class);
        $mockQuiz->uuid = 'test-quiz-uuid';

        // Mock conversation service
        $this->conversationService
            ->shouldReceive('findOrCreateConversation')
            ->once()
            ->andReturn($mockConversation);

        // Mock AI service
        $mockAIResponse = Mockery::mock();
        $mockAIResponse->content = json_encode([
            'title' => 'Photosynthesis Quiz',
            'questions' => [],
        ]);

        $this->aiService
            ->shouldReceive('sendMessage')
            ->once()
            ->andReturn($mockAIResponse);

        // Mock quiz service
        $this->quizService
            ->shouldReceive('create')
            ->once()
            ->andReturn($mockQuiz);

        // Act
        $result = $this->quizGenerationService->generateFromTopic($request);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('topic', $result['source_type']);
    }

    /** @test */
    public function it_validates_minimum_content_length_for_text_generation()
    {
        // Arrange
        $request = new Request([
            'content' => 'Short text', // Less than 100 characters
            'difficulty' => 'intermediate',
            'question_count' => 5,
        ]);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Content must be at least 100 characters long');

        $this->quizGenerationService->generateFromText($request);
    }

    /** @test */
    public function it_validates_question_count_limits()
    {
        // Arrange
        $request = new Request([
            'content' => str_repeat('This is sample content for testing. ', 10), // > 100 chars
            'difficulty' => 'intermediate',
            'question_count' => 100, // Exceeds maximum
        ]);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Question count must be between 1 and 50');

        $this->quizGenerationService->generateFromText($request);
    }

    /** @test */
    public function it_builds_correct_prompt_for_text_generation()
    {
        // This test would verify that the buildTextQuizPrompt method
        // creates the correct prompt structure for AI processing
        $this->assertTrue(true); // Placeholder for now
    }

    /** @test */
    public function it_handles_ai_service_errors_gracefully()
    {
        // Arrange
        $request = new Request([
            'content' => str_repeat('Sample content for testing. ', 10),
            'difficulty' => 'intermediate',
            'question_count' => 5,
        ]);

        $mockConversation = Mockery::mock(Conversation::class);
        $mockConversation->uuid = 'test-conversation-uuid';

        $this->conversationService
            ->shouldReceive('findOrCreateConversation')
            ->once()
            ->andReturn($mockConversation);

        // Mock AI service to throw exception
        $this->aiService
            ->shouldReceive('sendMessage')
            ->once()
            ->andThrow(new \Exception('AI service unavailable'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('AI service unavailable');

        $this->quizGenerationService->generateFromText($request);
    }
}
