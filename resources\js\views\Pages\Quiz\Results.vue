<template>
    <PageHeader
        :title="
            $trans(route.meta.trans, {
                attribute: $trans(route.meta.label),
            })
        "
        :navs="[
            {
                label: $trans('quiz.quiz'),
                path: 'QuizList',
            },
            {
                label: quiz.title || $trans('quiz.quiz'),
                path: 'QuizShow',
                params: { uuid: route.params.uuid },
            },
        ]"
    >
        <PageHeaderAction
            name="Quiz"
            :title="$trans('quiz.quiz')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <div v-if="quiz.uuid" class="space-y-6">
            <!-- Quiz Summary -->
            <BaseCard>
                <template #title>
                    {{ quiz.title }} - {{ $trans('quiz.results_summary') }}
                </template>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ stats.totalAttempts || 0 }}</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.total_attempts') }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">{{ stats.averageScore || 0 }}%</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.average_score') }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-orange-600">{{ stats.highestScore || 0 }}%</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.highest_score') }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-red-600">{{ stats.lowestScore || 0 }}%</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.lowest_score') }}</div>
                    </div>
                </div>
            </BaseCard>

            <!-- Results Table -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.attempt_results') }}
                </template>
                
                <DataTable
                    :header="attempts.headers"
                    :meta="attempts.meta"
                    module="quiz.attempt"
                    @refresh="fetchResults"
                >
                    <DataRow
                        v-for="attempt in attempts.data"
                        :key="attempt.uuid"
                        @double-click="viewAttemptDetails(attempt)"
                    >
                        <DataCell name="participant">
                            <div class="flex flex-col">
                                <span class="font-medium">{{ attempt.participant?.name || attempt.user?.name }}</span>
                                <span class="text-sm text-gray-500">{{ attempt.participant?.email || attempt.user?.email }}</span>
                            </div>
                        </DataCell>
                        <DataCell name="score">
                            <div class="flex items-center">
                                <span class="font-medium">{{ attempt.score }}%</span>
                                <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                    <div 
                                        class="h-2 rounded-full"
                                        :class="getScoreColor(attempt.score)"
                                        :style="{ width: `${attempt.score}%` }"
                                    ></div>
                                </div>
                            </div>
                        </DataCell>
                        <DataCell name="correctAnswers">
                            {{ attempt.correctAnswers }}/{{ attempt.totalQuestions }}
                        </DataCell>
                        <DataCell name="timeTaken">
                            {{ formatTime(attempt.timeTaken) }}
                        </DataCell>
                        <DataCell name="status">
                            <BaseBadge :design="getStatusDesign(attempt.status)">
                                {{ attempt.status.label }}
                            </BaseBadge>
                        </DataCell>
                        <DataCell name="submittedAt">
                            {{ attempt.submittedAt?.formatted || '-' }}
                        </DataCell>
                        <DataCell name="action">
                            <FloatingMenu>
                                <FloatingMenuItem
                                    icon="fas fa-eye"
                                    @click="viewAttemptDetails(attempt)"
                                >
                                    {{ $trans('quiz.view_details') }}
                                </FloatingMenuItem>
                                <FloatingMenuItem
                                    icon="fas fa-download"
                                    @click="downloadAttemptReport(attempt)"
                                >
                                    {{ $trans('quiz.download_report') }}
                                </FloatingMenuItem>
                            </FloatingMenu>
                        </DataCell>
                    </DataRow>
                </DataTable>
            </BaseCard>

            <!-- Export Options -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.export_options') }}
                </template>
                
                <div class="flex gap-4">
                    <BaseButton design="secondary" @click="exportToCSV">
                        <i class="fas fa-file-csv mr-2"></i>
                        {{ $trans('quiz.export_csv') }}
                    </BaseButton>
                    <BaseButton design="secondary" @click="exportToPDF">
                        <i class="fas fa-file-pdf mr-2"></i>
                        {{ $trans('quiz.export_pdf') }}
                    </BaseButton>
                    <BaseButton design="secondary" @click="exportToExcel">
                        <i class="fas fa-file-excel mr-2"></i>
                        {{ $trans('quiz.export_excel') }}
                    </BaseButton>
                </div>
            </BaseCard>
        </div>
    </ParentTransition>
</template>

<script>
export default {
    name: "QuizResults",
}
</script>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

const quiz = reactive({})
const attempts = reactive({})
const stats = reactive({})

const fetchResults = async () => {
    try {
        // TODO: Implement API calls to fetch quiz results
        console.log('Fetching results for quiz:', route.params.uuid)
    } catch (error) {
        console.error('Error fetching results:', error)
    }
}

const getScoreColor = (score) => {
    if (score >= 80) return 'bg-green-500'
    if (score >= 60) return 'bg-yellow-500'
    return 'bg-red-500'
}

const getStatusDesign = (status) => {
    switch (status.value) {
        case 'completed': return 'success'
        case 'in_progress': return 'warning'
        case 'abandoned': return 'danger'
        default: return 'secondary'
    }
}

const formatTime = (seconds) => {
    if (!seconds) return '-'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const viewAttemptDetails = (attempt) => {
    router.push({
        name: 'QuizAttemptResult',
        params: { uuid: attempt.uuid }
    })
}

const downloadAttemptReport = (attempt) => {
    // TODO: Implement download functionality
    console.log('Download report for attempt:', attempt.uuid)
}

const exportToCSV = () => {
    // TODO: Implement CSV export
    console.log('Export to CSV')
}

const exportToPDF = () => {
    // TODO: Implement PDF export
    console.log('Export to PDF')
}

const exportToExcel = () => {
    // TODO: Implement Excel export
    console.log('Export to Excel')
}

onMounted(() => {
    fetchResults()
})
</script>
