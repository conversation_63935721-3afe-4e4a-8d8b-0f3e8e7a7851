<template>
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                    {{ $trans('quiz.register_for_quiz') }}
                </h2>
                <p class="mt-2 text-sm text-gray-600" v-if="quiz.title">
                    {{ quiz.title }}
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Loading State -->
                <div v-if="loading" class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">{{ $trans('quiz.loading_quiz') }}</p>
                </div>

                <!-- Registration Form -->
                <form v-else @submit.prevent="submitRegistration" class="space-y-6">
                    <!-- Name Field -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            {{ $trans('quiz.participant.name') }} *
                        </label>
                        <div class="mt-1">
                            <input
                                id="name"
                                name="name"
                                type="text"
                                required
                                v-model="form.name"
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.name }"
                                :placeholder="$trans('quiz.participant.enter_name')"
                            />
                            <p v-if="errors.name" class="mt-2 text-sm text-red-600">
                                {{ errors.name }}
                            </p>
                        </div>
                    </div>

                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">
                            {{ $trans('quiz.participant.email') }} *
                        </label>
                        <div class="mt-1">
                            <input
                                id="email"
                                name="email"
                                type="email"
                                required
                                v-model="form.email"
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.email }"
                                :placeholder="$trans('quiz.participant.enter_email')"
                            />
                            <p v-if="errors.email" class="mt-2 text-sm text-red-600">
                                {{ errors.email }}
                            </p>
                        </div>
                    </div>

                    <!-- Phone Field (Optional) -->
                    <div v-if="quiz.requirePhone">
                        <label for="phone" class="block text-sm font-medium text-gray-700">
                            {{ $trans('quiz.participant.phone') }}
                            <span v-if="quiz.requirePhone">*</span>
                        </label>
                        <div class="mt-1">
                            <input
                                id="phone"
                                name="phone"
                                type="tel"
                                :required="quiz.requirePhone"
                                v-model="form.phone"
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.phone }"
                                :placeholder="$trans('quiz.participant.enter_phone')"
                            />
                            <p v-if="errors.phone" class="mt-2 text-sm text-red-600">
                                {{ errors.phone }}
                            </p>
                        </div>
                    </div>

                    <!-- Organization Field (Optional) -->
                    <div v-if="quiz.requireOrganization">
                        <label for="organization" class="block text-sm font-medium text-gray-700">
                            {{ $trans('quiz.participant.organization') }}
                            <span v-if="quiz.requireOrganization">*</span>
                        </label>
                        <div class="mt-1">
                            <input
                                id="organization"
                                name="organization"
                                type="text"
                                :required="quiz.requireOrganization"
                                v-model="form.organization"
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.organization }"
                                :placeholder="$trans('quiz.participant.enter_organization')"
                            />
                            <p v-if="errors.organization" class="mt-2 text-sm text-red-600">
                                {{ errors.organization }}
                            </p>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div v-if="quiz.termsAndConditions" class="flex items-start">
                        <div class="flex items-center h-5">
                            <input
                                id="terms"
                                name="terms"
                                type="checkbox"
                                required
                                v-model="form.acceptTerms"
                                class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                                :class="{ 'border-red-500': errors.acceptTerms }"
                            />
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="terms" class="text-gray-700">
                                {{ $trans('quiz.participant.accept_terms') }} *
                            </label>
                            <div class="mt-1 text-xs text-gray-500" v-html="quiz.termsAndConditions"></div>
                            <p v-if="errors.acceptTerms" class="mt-1 text-sm text-red-600">
                                {{ errors.acceptTerms }}
                            </p>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button
                            type="submit"
                            :disabled="submitting"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span v-if="submitting" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ $trans('quiz.registering') }}
                            </span>
                            <span v-else>
                                {{ $trans('quiz.register_and_start') }}
                            </span>
                        </button>
                    </div>

                    <!-- Back to Access -->
                    <div class="text-center">
                        <button
                            type="button"
                            @click="goBack"
                            class="text-sm text-blue-600 hover:text-blue-800"
                        >
                            {{ $trans('quiz.back_to_quiz') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "QuizPublicRegister",
}
</script>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const submitting = ref(false)
const quiz = reactive({})

const form = reactive({
    name: "",
    email: "",
    phone: "",
    organization: "",
    acceptTerms: false,
})

const errors = reactive({})

const fetchQuizData = async () => {
    try {
        loading.value = true
        // TODO: Implement API call to fetch quiz data by code
        console.log('Fetching quiz data for registration:', route.params.code)
    } catch (error) {
        console.error('Error fetching quiz data:', error)
    } finally {
        loading.value = false
    }
}

const submitRegistration = async () => {
    try {
        submitting.value = true
        Object.keys(errors).forEach(key => delete errors[key])

        // TODO: Implement API call to register participant
        console.log('Registering participant:', form)

        // Redirect to quiz taking page
        router.push({
            name: 'QuizPublicTake',
            params: { code: route.params.code }
        })
    } catch (error) {
        console.error('Error registering participant:', error)
        if (error.response?.data?.errors) {
            Object.assign(errors, error.response.data.errors)
        }
    } finally {
        submitting.value = false
    }
}

const goBack = () => {
    router.push({
        name: 'QuizPublicAccess',
        params: { code: route.params.code }
    })
}

onMounted(() => {
    fetchQuizData()
})
</script>
