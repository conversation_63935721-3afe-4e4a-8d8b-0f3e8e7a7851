<?php

namespace App\Policies\Quiz;

use App\Models\Quiz\QuizCategory;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class QuizCategoryPolicy
{
    use HandlesAuthorization;

    private function validateTeam(User $user, QuizCategory $category)
    {
        return $category->team_id == $user->current_team_id;
    }

    /**
     * Determine whether the user can request for pre-requisites.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function preRequisite(User $user)
    {
        return $user->canAny(['quiz-category:create', 'quiz-category:edit']);
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('quiz-category:read');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, QuizCategory $category)
    {
        if (!$this->validateTeam($user, $category)) {
            return false;
        }

        return $user->can('quiz-category:read');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('quiz-category:create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, QuizCategory $category)
    {
        if (!$this->validateTeam($user, $category)) {
            return false;
        }

        return $user->can('quiz-category:edit');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, QuizCategory $category)
    {
        if (!$this->validateTeam($user, $category)) {
            return false;
        }

        return $user->can('quiz-category:delete');
    }
}
