import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import category from "@stores/modules/quiz/category"
import generation from "@stores/modules/quiz/generation"
import attempt from "@stores/modules/quiz/attempt"
import collection from "@stores/modules/quiz/collection"
import { useToast } from "vue-toastification"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/quiz",
    formErrors: {},
})

const quiz = {
    namespaced: true,
    state: initialState,
    modules: {
        category,
        generation,
        attempt,
        collection,
    },
    mutations: {
        ...mutations,
    },
    actions: {
        ...actions,
        async create({ commit, state }, params) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(state.initURL, params)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async update({ commit, state }, { uuid, ...params }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.patch(`${state.initURL}/${uuid}`, params)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async duplicate({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(`${state.initURL}/${uuid}/duplicate`)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async publish({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.patch(`${state.initURL}/${uuid}/publish`)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async unpublish({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.patch(`${state.initURL}/${uuid}/unpublish`)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async generateShareLink({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(`${state.initURL}/${uuid}/share`)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async revokeSharing({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.delete(`${state.initURL}/${uuid}/share`)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getAnalytics({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/${uuid}/analytics`)
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getResults({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/${uuid}/results`)
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getPublicQuiz({ commit }, code) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`/app/quiz/public/${code}`)
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async registerForPublicQuiz({ commit }, { code, ...params }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(`/app/quiz/public/${code}/register`, params)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async discover({ commit }, params = {}) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get("/app/quiz/discover", { params })
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
    },
    getters: {
        ...getters,
    },
}

export default quiz
