import { createStore } from "vuex"

import setup from "@stores/modules/setup"
import navigation from "@stores/modules/navigation"
import config from "@stores/modules/config"
import layout from "@stores/modules/layout"
import dashboard from "@stores/modules/dashboard"
import image from "@stores/modules/image"
import auth from "@stores/modules/auth"
import user from "@stores/modules/user"
import team from "@stores/modules/team"
import chat from "@stores/modules/chat"
import customField from "@stores/modules/customField"
import utility from "@stores/modules/utility"
import option from "@stores/modules/option"
import tag from "@stores/modules/tag"
import moduleImport from "@stores/modules/moduleImport"
import subscription from "@stores/modules/subscription"

import onlineRegistration from "@stores/modules/student/onlineRegistration"
import guestPayment from "@stores/modules/student/guestPayment"
import reception from "@stores/modules/reception"
import academic from "@stores/modules/academic"
import student from "@stores/modules/student"
import finance from "@stores/modules/finance"
import exam from "@stores/modules/exam"
import employee from "@stores/modules/employee"
import attendanceAssistant from "@stores/modules/attendance"
import resource from "@stores/modules/resource"
import transport from "@stores/modules/transport"
import calendar from "@stores/modules/calendar"
import discipline from "@stores/modules/discipline"
import gallery from "@stores/modules/gallery"
import mess from "@stores/modules/mess"
import inventory from "@stores/modules/inventory"
import communication from "@stores/modules/communication"
import library from "@stores/modules/library"
import activity from "@stores/modules/activity"
import hostel from "@stores/modules/hostel"
import form from "@stores/modules/form"
import asset from "@stores/modules/asset"
import site from "@stores/modules/site"
import blog from "@stores/modules/blog"
import recruitment from "@stores/modules/recruitment"
import guardian from "@stores/modules/guardian"
import contact from "@stores/modules/contact"
import device from "@stores/modules/device"
import ai from "@stores/modules/ai"
import quiz from "@stores/modules/quiz"

const initialState = () => ({})

const store = createStore({
    modules: {
        setup,
        navigation,
        config,
        layout,
        dashboard,
        image,
        auth,
        user,
        chat,
        customField,
        utility,
        option,
        tag,
        moduleImport,
        subscription,
        onlineRegistration,
        guestPayment,
        team,
        reception,
        academic,
        student,
        finance,
        exam,
        employee,
        attendanceAssistant,
        resource,
        transport,
        calendar,
        discipline,
        gallery,
        mess,
        inventory,
        communication,
        library,
        activity,
        hostel,
        form,
        asset,
        site,
        blog,
        recruitment,
        guardian,
        contact,
        device,
        ai,
        quiz,
    },
    state: initialState,
    mutations: {},
    actions: {},
    getters: {},
})

export default store
