<template>
    <PageHeader
        :title="
            $trans(route.meta.trans, {
                attribute: $trans(route.meta.label),
            })
        "
        :navs="[
            {
                label: $trans('quiz.quiz'),
                path: 'QuizList',
            },
            {
                label: quiz.title || $trans('quiz.quiz'),
                path: 'QuizShow',
                params: { uuid: route.params.uuid },
            },
        ]"
    >
        <PageHeaderAction
            name="Quiz"
            :title="$trans('quiz.quiz')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <div v-if="quiz.uuid" class="space-y-6">
            <!-- Quiz Header -->
            <BaseCard>
                <template #title>
                    {{ quiz.title }}
                </template>
                <div class="space-y-4">
                    <p v-if="quiz.description" class="text-gray-600">
                        {{ quiz.description }}
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-question-circle mr-2 text-blue-500"></i>
                            <span>{{ quiz.questionsCount || 0 }} {{ $trans('quiz.questions') }}</span>
                        </div>
                        <div class="flex items-center" v-if="quiz.timeLimit">
                            <i class="fas fa-clock mr-2 text-orange-500"></i>
                            <span>{{ quiz.timeLimit }} {{ $trans('global.minutes') }}</span>
                        </div>
                        <div class="flex items-center" v-if="quiz.maxAttempts">
                            <i class="fas fa-redo mr-2 text-green-500"></i>
                            <span>{{ quiz.maxAttempts }} {{ $trans('quiz.max_attempts') }}</span>
                        </div>
                    </div>
                </div>
            </BaseCard>

            <!-- Quiz Questions Preview -->
            <BaseCard v-if="questions.length > 0">
                <template #title>
                    {{ $trans('quiz.questions_preview') }}
                </template>
                
                <div class="space-y-6">
                    <div 
                        v-for="(question, index) in questions" 
                        :key="question.uuid"
                        class="border-b border-gray-200 pb-6 last:border-b-0"
                    >
                        <div class="flex items-start justify-between mb-3">
                            <h4 class="font-medium text-lg">
                                {{ $trans('quiz.question') }} {{ index + 1 }}
                            </h4>
                            <span class="text-sm text-gray-500">
                                {{ question.marks || 1 }} {{ $trans('quiz.marks') }}
                            </span>
                        </div>
                        
                        <div class="mb-4">
                            <EnhancedContentDisplay :content="question.question" />
                        </div>

                        <!-- Multiple Choice Options -->
                        <div v-if="question.type === 'multiple_choice'" class="space-y-2">
                            <div 
                                v-for="(option, optionIndex) in question.options" 
                                :key="optionIndex"
                                class="flex items-center p-3 border rounded-lg"
                                :class="option.isCorrect ? 'bg-green-50 border-green-200' : 'bg-gray-50'"
                            >
                                <input 
                                    type="radio" 
                                    :name="`question_${question.uuid}`"
                                    class="mr-3"
                                    disabled
                                    :checked="option.isCorrect"
                                />
                                <span>{{ option.text }}</span>
                                <i v-if="option.isCorrect" class="fas fa-check text-green-500 ml-auto"></i>
                            </div>
                        </div>

                        <!-- True/False -->
                        <div v-else-if="question.type === 'true_false'" class="space-y-2">
                            <div class="flex items-center p-3 border rounded-lg"
                                 :class="question.correctAnswer === true ? 'bg-green-50 border-green-200' : 'bg-gray-50'">
                                <input type="radio" class="mr-3" disabled :checked="question.correctAnswer === true" />
                                <span>{{ $trans('global.true') }}</span>
                                <i v-if="question.correctAnswer === true" class="fas fa-check text-green-500 ml-auto"></i>
                            </div>
                            <div class="flex items-center p-3 border rounded-lg"
                                 :class="question.correctAnswer === false ? 'bg-green-50 border-green-200' : 'bg-gray-50'">
                                <input type="radio" class="mr-3" disabled :checked="question.correctAnswer === false" />
                                <span>{{ $trans('global.false') }}</span>
                                <i v-if="question.correctAnswer === false" class="fas fa-check text-green-500 ml-auto"></i>
                            </div>
                        </div>

                        <!-- Short Answer -->
                        <div v-else-if="question.type === 'short_answer'" class="space-y-2">
                            <BaseInput
                                :placeholder="$trans('quiz.your_answer')"
                                disabled
                                class="w-full"
                            />
                            <p class="text-sm text-green-600">
                                <strong>{{ $trans('quiz.correct_answer') }}:</strong> {{ question.correctAnswer }}
                            </p>
                        </div>

                        <!-- Essay -->
                        <div v-else-if="question.type === 'essay'" class="space-y-2">
                            <BaseTextarea
                                :placeholder="$trans('quiz.your_answer')"
                                disabled
                                rows="4"
                                class="w-full"
                            />
                            <p class="text-sm text-gray-600">
                                {{ $trans('quiz.essay_manual_grading') }}
                            </p>
                        </div>
                    </div>
                </div>
            </BaseCard>

            <!-- No Questions Message -->
            <BaseCard v-else>
                <div class="text-center py-8">
                    <i class="fas fa-question-circle text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        {{ $trans('quiz.no_questions') }}
                    </h3>
                    <p class="text-gray-500">
                        {{ $trans('quiz.add_questions_to_preview') }}
                    </p>
                </div>
            </BaseCard>
        </div>
    </ParentTransition>
</template>

<script>
export default {
    name: "QuizPreview",
}
</script>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"

const route = useRoute()
const router = useRouter()
const store = useStore()

const quiz = reactive({})
const questions = ref([])

const fetchQuizData = async () => {
    try {
        // TODO: Implement API call to fetch quiz and questions
        console.log('Fetching quiz data for:', route.params.uuid)
    } catch (error) {
        console.error('Error fetching quiz data:', error)
    }
}

onMounted(() => {
    fetchQuizData()
})
</script>
