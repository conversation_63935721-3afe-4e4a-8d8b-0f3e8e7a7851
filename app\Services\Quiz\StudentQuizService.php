<?php

namespace App\Services\Quiz;

use App\Models\Quiz\Quiz;
use App\Models\Quiz\QuizShare;
use App\Models\Quiz\StudentQuizCollection;
use App\Models\Quiz\QuizCollectionItem;
use App\Models\Student\Student;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class StudentQuizService
{
    public function preRequisite(Request $request): array
    {
        return [];
    }

    public function getStudentCollections(Student $student): array
    {
        $collections = StudentQuizCollection::query()
            ->byStudent($student->id)
            ->with(['items.quiz'])
            ->get();

        return $collections->toArray();
    }

    public function createCollection(Request $request, Student $student): StudentQuizCollection
    {
        \DB::beginTransaction();

        $collection = StudentQuizCollection::forceCreate([
            'student_id' => $student->id,
            'name' => $request->name,
            'description' => $request->description,
            'color' => $request->color,
            'is_default' => $request->boolean('is_default', false),
            'meta' => $request->meta ?? [],
        ]);

        // If this is set as default, unset other defaults
        if ($collection->is_default) {
            StudentQuizCollection::query()
                ->byStudent($student->id)
                ->where('id', '!=', $collection->id)
                ->update(['is_default' => false]);
        }

        \DB::commit();

        return $collection;
    }

    public function addQuizToCollection(Request $request, StudentQuizCollection $collection, Quiz $quiz): void
    {
        // Check if quiz is already in collection
        $exists = QuizCollectionItem::query()
            ->where('collection_id', $collection->id)
            ->where('quiz_id', $quiz->id)
            ->exists();

        if ($exists) {
            throw ValidationException::withMessages(['message' => trans('quiz.collection.quiz_already_in_collection')]);
        }

        \DB::beginTransaction();

        QuizCollectionItem::forceCreate([
            'collection_id' => $collection->id,
            'quiz_id' => $quiz->id,
            'position' => $request->position ?? 0,
            'added_at' => now(),
            'meta' => $request->meta ?? [],
        ]);

        \DB::commit();
    }

    public function removeQuizFromCollection(StudentQuizCollection $collection, Quiz $quiz): void
    {
        \DB::beginTransaction();

        QuizCollectionItem::query()
            ->where('collection_id', $collection->id)
            ->where('quiz_id', $quiz->id)
            ->delete();

        \DB::commit();
    }

    public function shareQuiz(Request $request, Quiz $quiz): QuizShare
    {
        \DB::beginTransaction();

        $share = QuizShare::forceCreate([
            'quiz_id' => $quiz->id,
            'shared_by_user_id' => auth()->id(),
            'shared_with_user_id' => $request->shared_with_user_id,
            'share_type' => 'quiz',
            'visibility' => $request->visibility ?? 'private',
            'message' => $request->message,
            'expires_at' => $request->expires_at,
            'is_active' => true,
            'meta' => $request->meta ?? [],
        ]);

        \DB::commit();

        return $share;
    }

    public function shareResult(Request $request, $attemptId): QuizShare
    {
        \DB::beginTransaction();

        $share = QuizShare::forceCreate([
            'attempt_id' => $attemptId,
            'shared_by_user_id' => auth()->id(),
            'shared_with_user_id' => $request->shared_with_user_id,
            'share_type' => 'result',
            'visibility' => $request->visibility ?? 'private',
            'message' => $request->message,
            'expires_at' => $request->expires_at,
            'is_active' => true,
            'meta' => $request->meta ?? [],
        ]);

        \DB::commit();

        return $share;
    }

    public function getDiscoverableQuizzes(Request $request): array
    {
        $quizzes = Quiz::query()
            ->published()
            ->discoverable()
            ->byTeam()
            ->with(['user', 'category', 'questions'])
            ->when($request->category_id, function ($q, $categoryId) {
                $q->where('category_id', $categoryId);
            })
            ->when($request->creator_type, function ($q, $creatorType) {
                $q->where('creator_type', $creatorType);
            })
            ->when($request->search, function ($q, $search) {
                $q->where(function ($query) use ($search) {
                    $query->where('title', 'like', "%{$search}%")
                          ->orWhere('description', 'like', "%{$search}%");
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return $quizzes->toArray();
    }

    public function getPeerSharedQuizzes(Request $request): array
    {
        $shares = QuizShare::query()
            ->active()
            ->notExpired()
            ->quizShares()
            ->where(function ($q) {
                $q->where('shared_with_user_id', auth()->id())
                  ->orWhere('visibility', 'team');
            })
            ->with(['quiz.user', 'quiz.category', 'sharedByUser'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return $shares->toArray();
    }

    public function getSharedResults(Request $request): array
    {
        $shares = QuizShare::query()
            ->active()
            ->notExpired()
            ->resultShares()
            ->where(function ($q) {
                $q->where('shared_with_user_id', auth()->id())
                  ->orWhere('visibility', 'team');
            })
            ->with(['attempt.quiz', 'attempt.user', 'sharedByUser'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return $shares->toArray();
    }

    public function getStudentAnalytics(Student $student): array
    {
        $totalQuizzes = Quiz::query()
            ->where('user_id', $student->user_id)
            ->count();

        $totalAttempts = \DB::table('quiz_attempts')
            ->join('quizzes', 'quizzes.id', '=', 'quiz_attempts.quiz_id')
            ->where('quiz_attempts.user_id', $student->user_id)
            ->count();

        $averageScore = \DB::table('quiz_attempts')
            ->join('quizzes', 'quizzes.id', '=', 'quiz_attempts.quiz_id')
            ->where('quiz_attempts.user_id', $student->user_id)
            ->where('quiz_attempts.status', 'completed')
            ->avg('quiz_attempts.percentage');

        $recentActivity = \DB::table('quiz_attempts')
            ->join('quizzes', 'quizzes.id', '=', 'quiz_attempts.quiz_id')
            ->where('quiz_attempts.user_id', $student->user_id)
            ->orderBy('quiz_attempts.created_at', 'desc')
            ->limit(10)
            ->get();

        return [
            'total_quizzes' => $totalQuizzes,
            'total_attempts' => $totalAttempts,
            'average_score' => round($averageScore ?? 0, 2),
            'recent_activity' => $recentActivity,
        ];
    }
}
