<template>
    <PageHeader
        :title="
            $trans(route.meta.trans, {
                attribute: $trans(route.meta.label),
            })
        "
        :navs="[
            {
                label: $trans('quiz.quiz'),
                path: 'QuizList',
            },
        ]"
    >
        <PageHeaderAction
            name="Quiz"
            :title="$trans('quiz.quiz')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <QuizForm></QuizForm>
    </ParentTransition>
</template>

<script>
export default {
    name: "QuizAction",
}
</script>

<script setup>
import { useRoute } from "vue-router"
import QuizForm from "./Form.vue"

const route = useRoute()
</script>
