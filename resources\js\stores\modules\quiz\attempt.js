import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import { useToast } from "vue-toastification"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/quiz/attempts",
    formErrors: {},
    currentAttempt: null,
    attempts: [],
    timeRemaining: null,
    autoSaveInterval: null,
})

const attempt = {
    namespaced: true,
    state: initialState,
    mutations: {
        ...mutations,
        SET_CURRENT_ATTEMPT(state, attempt) {
            state.currentAttempt = attempt
        },
        SET_ATTEMPTS(state, attempts) {
            state.attempts = attempts
        },
        ADD_ATTEMPT(state, attempt) {
            state.attempts.unshift(attempt)
        },
        UPDATE_ATTEMPT(state, updatedAttempt) {
            const index = state.attempts.findIndex(att => att.uuid === updatedAttempt.uuid)
            if (index !== -1) {
                state.attempts.splice(index, 1, updatedAttempt)
            }
            if (state.currentAttempt && state.currentAttempt.uuid === updatedAttempt.uuid) {
                state.currentAttempt = updatedAttempt
            }
        },
        SET_TIME_REMAINING(state, time) {
            state.timeRemaining = time
        },
        SET_AUTO_SAVE_INTERVAL(state, interval) {
            if (state.autoSaveInterval) {
                clearInterval(state.autoSaveInterval)
            }
            state.autoSaveInterval = interval
        },
        CLEAR_AUTO_SAVE_INTERVAL(state) {
            if (state.autoSaveInterval) {
                clearInterval(state.autoSaveInterval)
                state.autoSaveInterval = null
            }
        },
        UPDATE_ANSWER(state, { questionUuid, answer }) {
            if (state.currentAttempt && state.currentAttempt.answers) {
                const existingAnswerIndex = state.currentAttempt.answers.findIndex(
                    ans => ans.questionUuid === questionUuid
                )
                
                if (existingAnswerIndex !== -1) {
                    state.currentAttempt.answers[existingAnswerIndex].answer = answer
                    state.currentAttempt.answers[existingAnswerIndex].updatedAt = new Date().toISOString()
                } else {
                    state.currentAttempt.answers.push({
                        questionUuid,
                        answer,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString(),
                    })
                }
            }
        },
    },
    actions: {
        ...actions,
        async startAttempt({ commit, state }, quizUuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(`/app/quiz/${quizUuid}/attempts`)
                commit("SET_CURRENT_ATTEMPT", response.attempt)
                commit("SET_LOADING", false)
                
                // Start auto-save if quiz has time limit
                if (response.attempt.quiz.timeLimit) {
                    dispatch('startAutoSave')
                    dispatch('startTimer', response.attempt.quiz.timeLimit)
                }
                
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async submitAttempt({ commit, state, dispatch }, { uuid, answers = null }) {
            try {
                commit("SET_LOADING", true)
                
                // Use provided answers or current attempt answers
                const submissionData = {
                    answers: answers || (state.currentAttempt ? state.currentAttempt.answers : []),
                }
                
                const response = await Api.post(`${state.initURL}/${uuid}/submit`, submissionData)
                commit("UPDATE_ATTEMPT", response.attempt)
                commit("SET_LOADING", false)
                
                // Clear auto-save and timer
                dispatch('stopAutoSave')
                dispatch('stopTimer')
                
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async saveProgress({ commit, state }, { uuid, answers }) {
            try {
                const response = await Api.post(`${state.initURL}/${uuid}/save-progress`, { answers })
                commit("UPDATE_ATTEMPT", response.attempt)
                return response
            } catch (error) {
                // Don't show error toast for auto-save failures
                console.warn('Failed to save progress:', error)
                throw error
            }
        },
        async getAttempt({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/${uuid}`)
                commit("SET_CURRENT_ATTEMPT", response.attempt)
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getAttempts({ commit, state }, params = {}) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(state.initURL, { params })
                commit("SET_ATTEMPTS", response.attempts || response.data || [])
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getQuizAttempts({ commit, state }, quizUuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`/app/quiz/${quizUuid}/attempts`)
                commit("SET_ATTEMPTS", response.attempts || [])
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        updateAnswer({ commit }, { questionUuid, answer }) {
            commit("UPDATE_ANSWER", { questionUuid, answer })
        },
        startAutoSave({ commit, state, dispatch }) {
            if (state.currentAttempt) {
                const interval = setInterval(() => {
                    if (state.currentAttempt && state.currentAttempt.answers.length > 0) {
                        dispatch('saveProgress', {
                            uuid: state.currentAttempt.uuid,
                            answers: state.currentAttempt.answers,
                        }).catch(() => {
                            // Auto-save failed, but don't interrupt user
                        })
                    }
                }, 30000) // Auto-save every 30 seconds
                
                commit("SET_AUTO_SAVE_INTERVAL", interval)
            }
        },
        stopAutoSave({ commit }) {
            commit("CLEAR_AUTO_SAVE_INTERVAL")
        },
        startTimer({ commit }, timeLimit) {
            // Convert minutes to seconds
            let timeRemaining = timeLimit * 60
            commit("SET_TIME_REMAINING", timeRemaining)
            
            const timer = setInterval(() => {
                timeRemaining--
                commit("SET_TIME_REMAINING", timeRemaining)
                
                if (timeRemaining <= 0) {
                    clearInterval(timer)
                    // Auto-submit when time expires
                    // This should be handled by the component
                }
            }, 1000)
        },
        stopTimer({ commit }) {
            commit("SET_TIME_REMAINING", null)
        },
    },
    getters: {
        ...getters,
        currentAttempt: (state) => state.currentAttempt,
        attempts: (state) => state.attempts,
        timeRemaining: (state) => state.timeRemaining,
        timeRemainingFormatted: (state) => {
            if (!state.timeRemaining) return null
            
            const minutes = Math.floor(state.timeRemaining / 60)
            const seconds = state.timeRemaining % 60
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        },
        getAnswerForQuestion: (state) => (questionUuid) => {
            if (!state.currentAttempt || !state.currentAttempt.answers) return null
            
            const answer = state.currentAttempt.answers.find(ans => ans.questionUuid === questionUuid)
            return answer ? answer.answer : null
        },
        completedQuestionsCount: (state) => {
            if (!state.currentAttempt || !state.currentAttempt.answers) return 0
            
            return state.currentAttempt.answers.filter(ans => 
                ans.answer && ans.answer.toString().trim() !== ''
            ).length
        },
        isTimeExpired: (state) => {
            return state.timeRemaining !== null && state.timeRemaining <= 0
        },
    },
}

export default attempt
