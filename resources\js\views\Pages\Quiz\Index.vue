<template>
    <ListItem :init-url="initUrl" @setItems="setItems">
        <template #header>
            <PageHeader
                :title="$trans('quiz.quiz')"
                :navs="[]"
            >
                <PageHeaderAction
                    url="quiz/"
                    name="Quiz"
                    :title="$trans('quiz.quiz')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                />
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <FilterForm
                    @refresh="emitter.emit('listItems')"
                    @hide="showFilter = false"
                ></FilterForm>
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="quizzes.headers"
                :meta="quizzes.meta"
                module="quiz"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="quiz in quizzes.data"
                    :key="quiz.uuid"
                    @double-click="
                        router.push({
                            name: 'QuizShow',
                            params: { uuid: quiz.uuid },
                        })
                    "
                >
                    <DataCell name="title">
                        <div class="flex flex-col">
                            <span class="font-medium">{{ quiz.title }}</span>
                            <span class="text-sm text-gray-500" v-if="quiz.description">
                                {{ quiz.description }}
                            </span>
                        </div>
                    </DataCell>
                    <DataCell name="category">
                        {{ quiz.category?.name || '-' }}
                    </DataCell>
                    <DataCell name="questionsCount">
                        {{ quiz.questionsCount || 0 }}
                    </DataCell>
                    <DataCell name="attemptsCount">
                        {{ quiz.attemptsCount || 0 }}
                    </DataCell>
                    <DataCell name="isPublic">
                        <BaseBadge :design="quiz.isPublic ? 'success' : 'secondary'">
                            {{ quiz.isPublic ? $trans('global.yes') : $trans('global.no') }}
                        </BaseBadge>
                    </DataCell>
                    <DataCell name="createdAt">
                        {{ quiz.createdAt.formatted }}
                    </DataCell>
                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                icon="fas fa-eye"
                                @click="
                                    router.push({
                                        name: 'QuizShow',
                                        params: { uuid: quiz.uuid },
                                    })
                                "
                            >
                                {{ $trans('global.show') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                icon="fas fa-play"
                                @click="
                                    router.push({
                                        name: 'QuizPreview',
                                        params: { uuid: quiz.uuid },
                                    })
                                "
                            >
                                {{ $trans('quiz.actions.preview') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                icon="fas fa-chart-bar"
                                @click="
                                    router.push({
                                        name: 'QuizResults',
                                        params: { uuid: quiz.uuid },
                                    })
                                "
                            >
                                {{ $trans('quiz.actions.view_results') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                icon="fas fa-edit"
                                @click="
                                    router.push({
                                        name: 'QuizEdit',
                                        params: { uuid: quiz.uuid },
                                    })
                                "
                            >
                                {{ $trans('general.edit') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                icon="fas fa-copy"
                                @click="duplicateQuiz(quiz)"
                            >
                                {{ $trans('general.duplicate') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: quiz.uuid,
                                    })
                                "
                            >
                                {{ $trans('general.delete') }}
                            </FloatingMenuItem>
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
                <template #actionButton>
                    <BaseButton @click="router.push({ name: 'QuizCreate' })">
                        {{
                            $trans("global.add", {
                                attribute: $trans("quiz.quiz"),
                            })
                        }}
                    </BaseButton>
                </template>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "QuizList",
}
</script>

<script setup>
import { ref, reactive } from "vue"
import { useRouter } from "vue-router"
import { useStore } from "vuex"
import { useEmitter } from "@core/composables"
import { perform } from "@core/helpers"
import FilterForm from "./FilterForm.vue"

const router = useRouter()
const store = useStore()
const emitter = useEmitter()

const initUrl = "quiz/"
const showFilter = ref(false)
const quizzes = reactive({})

const userActions = ["create"]
const dropdownActions = ["filter"]

const setItems = (data) => {
    Object.assign(quizzes, data)
}

const duplicateQuiz = (quiz) => {
    // TODO: Implement quiz duplication logic
    console.log('Duplicate quiz:', quiz)
}
</script>
