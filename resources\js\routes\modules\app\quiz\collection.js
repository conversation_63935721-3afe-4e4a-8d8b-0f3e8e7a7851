export default [
    {
        path: "collections",
        name: "QuizCollection",
        redirect: { name: "QuizCollectionList" },
        meta: {
            label: "quiz.collection.collections",
            icon: "fas fa-folder",
            permissions: ["quiz:read"],
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "",
                name: "QuizCollectionList",
                meta: {
                    trans: "global.list",
                    label: "quiz.collection.collections",
                    keySearch: true,
                },
                component: () => import("@views/Pages/Quiz/Collection/Index.vue"),
            },
            {
                path: "create",
                name: "QuizCollectionCreate",
                meta: {
                    type: "create",
                    action: "create",
                    trans: "global.add",
                    label: "quiz.collection.collection",
                    permissions: ["quiz:create"],
                },
                component: () => import("@views/Pages/Quiz/Collection/Action.vue"),
            },
            {
                path: ":uuid/edit",
                name: "QuizCollectionEdit",
                meta: {
                    type: "edit",
                    action: "update",
                    trans: "global.edit",
                    label: "quiz.collection.collection",
                    permissions: ["quiz:edit"],
                },
                component: () => import("@views/Pages/Quiz/Collection/Action.vue"),
            },
            {
                path: ":uuid",
                name: "QuizCollectionShow",
                meta: {
                    trans: "global.show",
                    label: "quiz.collection.collection",
                },
                component: () => import("@views/Pages/Quiz/Collection/Show.vue"),
            },
        ],
    },
    {
        path: "discover",
        name: "QuizDiscover",
        meta: {
            label: "quiz.discovery.discover_quizzes",
            icon: "fas fa-compass",
            permissions: ["quiz:read"],
        },
        component: () => import("@views/Pages/Quiz/Discover/Index.vue"),
    },
]
