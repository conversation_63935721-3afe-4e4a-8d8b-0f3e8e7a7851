<?php

use App\Http\Controllers\Quiz\QuizController;
use App\Http\Controllers\Quiz\QuizCategoryController;
use App\Http\Controllers\Quiz\QuizAttemptController;
use App\Http\Controllers\Quiz\QuizGenerationController;
use App\Http\Controllers\Quiz\StudentQuizController;
use App\Http\Controllers\Quiz\QuizShareController;
use App\Http\Controllers\Quiz\QuizParticipantController;
use Illuminate\Support\Facades\Route;

// Quiz Management Routes (Teachers/Admins)
Route::prefix('quiz')->name('quiz.')->middleware('permission:quiz:read')->group(function () {
    
    // Quiz CRUD
    Route::get('pre-requisite', [QuizController::class, 'preRequisite'])->name('preRequisite');
    Route::get('', [QuizController::class, 'index'])->name('index');
    Route::post('', [QuizController::class, 'store'])->middleware('permission:quiz:create')->name('store');
    Route::get('{quiz}', [QuizController::class, 'show'])->name('show');
    Route::patch('{quiz}', [QuizController::class, 'update'])->middleware('permission:quiz:edit')->name('update');
    Route::delete('{quiz}', [QuizController::class, 'destroy'])->middleware('permission:quiz:delete')->name('destroy');
    
    // Quiz Actions
    Route::post('{quiz}/publish', [QuizController::class, 'publish'])->middleware('permission:quiz:edit')->name('publish');
    Route::post('{quiz}/unpublish', [QuizController::class, 'unpublish'])->middleware('permission:quiz:edit')->name('unpublish');
    Route::post('{quiz}/duplicate', [QuizController::class, 'duplicate'])->middleware('permission:quiz:create')->name('duplicate');
    Route::get('{quiz}/preview', [QuizController::class, 'preview'])->name('preview');
    Route::get('{quiz}/results', [QuizController::class, 'results'])->name('results');
    Route::get('{quiz}/analytics', [QuizController::class, 'analytics'])->name('analytics');
    
    // Quiz Import/Export
    Route::post('import', [QuizController::class, 'import'])->middleware('permission:quiz:create')->name('import');
    Route::get('{quiz}/export', [QuizController::class, 'export'])->name('export');
});

// Quiz Categories
Route::prefix('quiz/categories')->name('quiz.categories.')->middleware('permission:quiz:read')->group(function () {
    Route::get('pre-requisite', [QuizCategoryController::class, 'preRequisite'])->name('preRequisite');
    Route::get('', [QuizCategoryController::class, 'index'])->name('index');
    Route::post('', [QuizCategoryController::class, 'store'])->middleware('permission:quiz:create')->name('store');
    Route::get('{quizCategory}', [QuizCategoryController::class, 'show'])->name('show');
    Route::patch('{quizCategory}', [QuizCategoryController::class, 'update'])->middleware('permission:quiz:edit')->name('update');
    Route::delete('{quizCategory}', [QuizCategoryController::class, 'destroy'])->middleware('permission:quiz:delete')->name('destroy');
});

// AI Quiz Generation Routes
Route::prefix('quiz/generate')->name('quiz.generate.')->middleware('permission:ai:generate-quiz')->group(function () {
    Route::get('pre-requisite', [QuizGenerationController::class, 'preRequisite'])->name('preRequisite');
    Route::post('text', [QuizGenerationController::class, 'generateFromText'])->name('text');
    Route::post('topic', [QuizGenerationController::class, 'generateFromTopic'])->name('topic');
    Route::post('url', [QuizGenerationController::class, 'generateFromUrl'])->name('url');
    Route::post('document', [QuizGenerationController::class, 'generateFromDocument'])->name('document');
});

// Quiz Attempts (Taking Quizzes)
Route::prefix('quiz/attempts')->name('quiz.attempts.')->group(function () {
    Route::get('pre-requisite', [QuizAttemptController::class, 'preRequisite'])->name('preRequisite');
    Route::get('', [QuizAttemptController::class, 'index'])->name('index');
    Route::post('{quiz}/start', [QuizAttemptController::class, 'start'])->name('start');
    Route::get('{quizAttempt}', [QuizAttemptController::class, 'show'])->name('show');
    Route::patch('{quizAttempt}/submit', [QuizAttemptController::class, 'submit'])->name('submit');
    Route::get('{quizAttempt}/result', [QuizAttemptController::class, 'result'])->name('result');
    Route::post('{quizAttempt}/save-progress', [QuizAttemptController::class, 'saveProgress'])->name('saveProgress');
});

// Student Quiz Collections (Personal Quizzes)
Route::prefix('student/quiz')->name('student.quiz.')->middleware('permission:quiz:read')->group(function () {
    Route::get('collections/pre-requisite', [StudentQuizController::class, 'preRequisite'])->name('collections.preRequisite');
    Route::get('collections', [StudentQuizController::class, 'index'])->name('collections.index');
    Route::post('collections', [StudentQuizController::class, 'store'])->middleware('permission:quiz:create')->name('collections.store');
    Route::get('collections/{studentQuizCollection}', [StudentQuizController::class, 'show'])->name('collections.show');
    Route::patch('collections/{studentQuizCollection}', [StudentQuizController::class, 'update'])->middleware('permission:quiz:edit')->name('collections.update');
    Route::delete('collections/{studentQuizCollection}', [StudentQuizController::class, 'destroy'])->middleware('permission:quiz:delete')->name('collections.destroy');
    
    // Collection Items
    Route::post('collections/{studentQuizCollection}/items', [StudentQuizController::class, 'addItem'])->name('collections.addItem');
    Route::delete('collections/{studentQuizCollection}/items/{quizCollectionItem}', [StudentQuizController::class, 'removeItem'])->name('collections.removeItem');
    
    // Discovery and Sharing
    Route::get('discover', [StudentQuizController::class, 'discover'])->name('discover');
    Route::post('{quiz}/add-to-collection', [StudentQuizController::class, 'addToCollection'])->name('addToCollection');
});

// Quiz Sharing
Route::prefix('quiz/share')->name('quiz.share.')->group(function () {
    Route::post('{quiz}/generate-link', [QuizShareController::class, 'generateShareLink'])->name('generateLink');
    Route::get('{quiz}/share-settings', [QuizShareController::class, 'getShareSettings'])->name('getSettings');
    Route::patch('{quiz}/share-settings', [QuizShareController::class, 'updateShareSettings'])->name('updateSettings');
    Route::delete('{quiz}/revoke-sharing', [QuizShareController::class, 'revokeSharing'])->name('revokeSharing');
    Route::get('shared/{shareCode}', [QuizShareController::class, 'getSharedQuiz'])->name('getShared');
});

// Public Quiz Access (No authentication required)
Route::prefix('public/quiz')->name('public.quiz.')->group(function () {
    Route::get('{uniqueCode}', [QuizController::class, 'getPublicQuiz'])->name('get');
    Route::post('{uniqueCode}/register', [QuizParticipantController::class, 'register'])->name('register');
    Route::post('{uniqueCode}/attempt', [QuizAttemptController::class, 'startPublicAttempt'])->name('attempt');
    Route::get('attempt/{quizAttempt}/questions', [QuizAttemptController::class, 'getPublicQuestions'])->name('questions');
    Route::patch('attempt/{quizAttempt}/submit', [QuizAttemptController::class, 'submitPublicAttempt'])->name('submit');
    Route::get('attempt/{quizAttempt}/result', [QuizAttemptController::class, 'getPublicResult'])->name('result');
});

// Quiz Participants Management
Route::prefix('quiz/{quiz}/participants')->name('quiz.participants.')->middleware('permission:quiz:read')->group(function () {
    Route::get('', [QuizParticipantController::class, 'index'])->name('index');
    Route::post('approve/{quizParticipant}', [QuizParticipantController::class, 'approve'])->middleware('permission:quiz:edit')->name('approve');
    Route::post('reject/{quizParticipant}', [QuizParticipantController::class, 'reject'])->middleware('permission:quiz:edit')->name('reject');
    Route::delete('{quizParticipant}', [QuizParticipantController::class, 'destroy'])->middleware('permission:quiz:delete')->name('destroy');
});
