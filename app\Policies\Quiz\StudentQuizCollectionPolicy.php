<?php

namespace App\Policies\Quiz;

use App\Models\Quiz\StudentQuizCollection;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class StudentQuizCollectionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can request for pre-requisites.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function preRequisite(User $user)
    {
        return $user->hasAnyRole(['student']) && $user->can('quiz:create-collection');
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasAnyRole(['student']) && $user->can('quiz:view-collections');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, StudentQuizCollection $collection)
    {
        if (!$user->hasAnyRole(['student'])) {
            return false;
        }

        if (!$user->can('quiz:view-collections')) {
            return false;
        }

        // Students can only view their own collections
        return $collection->student_id === $user->student?->id;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasAnyRole(['student']) && $user->can('quiz:create-collection');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, StudentQuizCollection $collection)
    {
        if (!$user->hasAnyRole(['student'])) {
            return false;
        }

        if (!$user->can('quiz:edit-collection')) {
            return false;
        }

        // Students can only update their own collections
        return $collection->student_id === $user->student?->id;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, StudentQuizCollection $collection)
    {
        if (!$user->hasAnyRole(['student'])) {
            return false;
        }

        if (!$user->can('quiz:delete-collection')) {
            return false;
        }

        // Students can only delete their own collections
        // Cannot delete default collection
        return $collection->student_id === $user->student?->id && !$collection->is_default;
    }

    /**
     * Determine whether the user can add quizzes to the collection.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function addQuiz(User $user, StudentQuizCollection $collection)
    {
        if (!$user->hasAnyRole(['student'])) {
            return false;
        }

        if (!$user->can('quiz:manage-collection')) {
            return false;
        }

        // Students can only add quizzes to their own collections
        return $collection->student_id === $user->student?->id;
    }

    /**
     * Determine whether the user can remove quizzes from the collection.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function removeQuiz(User $user, StudentQuizCollection $collection)
    {
        if (!$user->hasAnyRole(['student'])) {
            return false;
        }

        if (!$user->can('quiz:manage-collection')) {
            return false;
        }

        // Students can only remove quizzes from their own collections
        return $collection->student_id === $user->student?->id;
    }
}
