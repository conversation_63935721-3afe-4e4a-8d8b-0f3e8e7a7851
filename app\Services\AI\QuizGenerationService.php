<?php

namespace App\Services\AI;

use App\Models\Quiz\Quiz;
use App\Models\Quiz\QuizQuestion;
use App\Models\Quiz\QuizCategory;
use App\Services\AI\AIService;
use App\Services\AI\ConversationService;
use App\Services\Quiz\QuizService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class QuizGenerationService
{
    protected AIService $aiService;
    protected ConversationService $conversationService;
    protected QuizService $quizService;

    public function __construct(
        AIService $aiService,
        ConversationService $conversationService,
        QuizService $quizService
    ) {
        $this->aiService = $aiService;
        $this->conversationService = $conversationService;
        $this->quizService = $quizService;
    }

    /**
     * Generate quiz from text content
     */
    public function generateFromText(Request $request): array
    {
        $content = $request->content;
        $title = $request->title ?? 'AI Generated Quiz';
        $difficulty = $request->difficulty ?? 'intermediate';
        $questionCount = $request->question_count ?? 10;
        $questionTypes = $request->question_types ?? ['mcq', 'true_false'];

        // Create conversation for quiz generation
        $conversation = $this->conversationService->findOrCreateConversation(
            'academic_chat',
            'quiz_generation_text',
            [
                'source_type' => 'text',
                'difficulty' => $difficulty,
                'question_count' => $questionCount,
            ]
        );

        // Build enhanced prompt for text-based quiz generation
        $prompt = $this->buildTextQuizPrompt($content, $title, $difficulty, $questionCount, $questionTypes);

        // Send to AI
        $response = $this->aiService->sendMessage($prompt, $conversation);

        // Parse AI response and create quiz
        $quiz = $this->createQuizFromAIResponse($response, $request, 'text');

        return [
            'conversation_id' => $conversation->uuid,
            'quiz' => $quiz,
            'ai_response' => $response,
            'source_type' => 'text',
        ];
    }

    /**
     * Generate quiz from subject/topic
     */
    public function generateFromTopic(Request $request): array
    {
        $topic = $request->topic;
        $subject = $request->subject;
        $level = $request->level;
        $difficulty = $request->difficulty ?? 'intermediate';
        $questionCount = $request->question_count ?? 10;
        $questionTypes = $request->question_types ?? ['mcq', 'true_false'];

        // Create conversation for quiz generation
        $conversation = $this->conversationService->findOrCreateConversation(
            'academic_chat',
            'quiz_generation_topic',
            [
                'subject' => $subject,
                'level' => $level,
                'topic' => $topic,
                'difficulty' => $difficulty,
            ]
        );

        // Build enhanced prompt for topic-based quiz generation
        $prompt = $this->buildTopicQuizPrompt($topic, $subject, $level, $difficulty, $questionCount, $questionTypes);

        // Send to AI
        $response = $this->aiService->sendMessage($prompt, $conversation);

        // Parse AI response and create quiz
        $quiz = $this->createQuizFromAIResponse($response, $request, 'topic');

        return [
            'conversation_id' => $conversation->uuid,
            'quiz' => $quiz,
            'ai_response' => $response,
            'source_type' => 'topic',
        ];
    }

    /**
     * Generate quiz from URL content
     */
    public function generateFromUrl(Request $request): array
    {
        $url = $request->url;
        $title = $request->title ?? 'Quiz from Web Content';
        $difficulty = $request->difficulty ?? 'intermediate';
        $questionCount = $request->question_count ?? 10;
        $questionTypes = $request->question_types ?? ['mcq', 'true_false'];

        // Extract content from URL
        $extractedContent = $this->extractContentFromUrl($url);

        if (!$extractedContent) {
            throw ValidationException::withMessages(['url' => 'Could not extract content from the provided URL']);
        }

        // Create conversation for quiz generation
        $conversation = $this->conversationService->findOrCreateConversation(
            'academic_chat',
            'quiz_generation_url',
            [
                'source_url' => $url,
                'difficulty' => $difficulty,
                'question_count' => $questionCount,
            ]
        );

        // Build enhanced prompt for URL-based quiz generation
        $prompt = $this->buildUrlQuizPrompt($extractedContent, $url, $title, $difficulty, $questionCount, $questionTypes);

        // Send to AI
        $response = $this->aiService->sendMessage($prompt, $conversation);

        // Parse AI response and create quiz
        $quiz = $this->createQuizFromAIResponse($response, $request, 'url');

        return [
            'conversation_id' => $conversation->uuid,
            'quiz' => $quiz,
            'ai_response' => $response,
            'source_type' => 'url',
            'source_url' => $url,
            'extracted_content' => Str::limit($extractedContent, 500),
        ];
    }

    /**
     * Generate quiz from uploaded document
     */
    public function generateFromDocument(Request $request): array
    {
        $file = $request->file('document');
        $title = $request->title ?? 'Quiz from Document';
        $difficulty = $request->difficulty ?? 'intermediate';
        $questionCount = $request->question_count ?? 10;
        $questionTypes = $request->question_types ?? ['mcq', 'true_false'];

        // Extract content from document
        $extractedContent = $this->extractContentFromDocument($file);

        if (!$extractedContent) {
            throw ValidationException::withMessages(['document' => 'Could not extract content from the uploaded document']);
        }

        // Create conversation for quiz generation
        $conversation = $this->conversationService->findOrCreateConversation(
            'academic_chat',
            'quiz_generation_document',
            [
                'source_file' => $file->getClientOriginalName(),
                'difficulty' => $difficulty,
                'question_count' => $questionCount,
            ]
        );

        // Build enhanced prompt for document-based quiz generation
        $prompt = $this->buildDocumentQuizPrompt($extractedContent, $file->getClientOriginalName(), $title, $difficulty, $questionCount, $questionTypes);

        // Send to AI
        $response = $this->aiService->sendMessage($prompt, $conversation);

        // Parse AI response and create quiz
        $quiz = $this->createQuizFromAIResponse($response, $request, 'document');

        return [
            'conversation_id' => $conversation->uuid,
            'quiz' => $quiz,
            'ai_response' => $response,
            'source_type' => 'document',
            'source_file' => $file->getClientOriginalName(),
            'extracted_content' => Str::limit($extractedContent, 500),
        ];
    }

    /**
     * Build enhanced prompt for text-based quiz generation
     */
    private function buildTextQuizPrompt(string $content, string $title, string $difficulty, int $questionCount, array $questionTypes): string
    {
        $prompt = "Generate a quiz from the following text content:\n\n";
        $prompt .= "CONTENT:\n{$content}\n\n";
        $prompt .= "QUIZ SPECIFICATIONS:\n";
        $prompt .= "- Title: {$title}\n";
        $prompt .= "- Difficulty Level: {$difficulty}\n";
        $prompt .= "- Number of Questions: {$questionCount}\n";
        $prompt .= "- Question Types: " . implode(', ', $questionTypes) . "\n\n";

        $prompt .= $this->getDifficultyGuidelines($difficulty);
        $prompt .= $this->getQuestionTypeGuidelines($questionTypes);
        $prompt .= $this->getOutputFormatGuidelines();

        return $prompt;
    }

    /**
     * Build enhanced prompt for topic-based quiz generation
     */
    private function buildTopicQuizPrompt(string $topic, string $subject, string $level, string $difficulty, int $questionCount, array $questionTypes): string
    {
        $prompt = "Generate a comprehensive quiz on the following topic:\n\n";
        $prompt .= "TOPIC: {$topic}\n";
        $prompt .= "SUBJECT: {$subject}\n";
        $prompt .= "ACADEMIC LEVEL: {$level}\n";
        $prompt .= "DIFFICULTY: {$difficulty}\n";
        $prompt .= "NUMBER OF QUESTIONS: {$questionCount}\n";
        $prompt .= "QUESTION TYPES: " . implode(', ', $questionTypes) . "\n\n";

        $prompt .= "CURRICULUM ALIGNMENT:\n";
        $prompt .= "- Align with Nigerian Cambridge curriculum standards\n";
        $prompt .= "- Ensure age-appropriate content for {$level} level\n";
        $prompt .= "- Include relevant real-world applications\n\n";

        $prompt .= $this->getDifficultyGuidelines($difficulty);
        $prompt .= $this->getQuestionTypeGuidelines($questionTypes);
        $prompt .= $this->getOutputFormatGuidelines();

        return $prompt;
    }

    /**
     * Build enhanced prompt for URL-based quiz generation
     */
    private function buildUrlQuizPrompt(string $content, string $url, string $title, string $difficulty, int $questionCount, array $questionTypes): string
    {
        $prompt = "Generate a quiz from the following web content:\n\n";
        $prompt .= "SOURCE URL: {$url}\n\n";
        $prompt .= "EXTRACTED CONTENT:\n{$content}\n\n";
        $prompt .= "QUIZ SPECIFICATIONS:\n";
        $prompt .= "- Title: {$title}\n";
        $prompt .= "- Difficulty Level: {$difficulty}\n";
        $prompt .= "- Number of Questions: {$questionCount}\n";
        $prompt .= "- Question Types: " . implode(', ', $questionTypes) . "\n\n";

        $prompt .= "CONTENT GUIDELINES:\n";
        $prompt .= "- Focus on the main concepts and key information from the web content\n";
        $prompt .= "- Ensure questions test understanding rather than memorization\n";
        $prompt .= "- Include source attribution where appropriate\n\n";

        $prompt .= $this->getDifficultyGuidelines($difficulty);
        $prompt .= $this->getQuestionTypeGuidelines($questionTypes);
        $prompt .= $this->getOutputFormatGuidelines();

        return $prompt;
    }

    /**
     * Build enhanced prompt for document-based quiz generation
     */
    private function buildDocumentQuizPrompt(string $content, string $filename, string $title, string $difficulty, int $questionCount, array $questionTypes): string
    {
        $prompt = "Generate a quiz from the following document content:\n\n";
        $prompt .= "SOURCE DOCUMENT: {$filename}\n\n";
        $prompt .= "DOCUMENT CONTENT:\n{$content}\n\n";
        $prompt .= "QUIZ SPECIFICATIONS:\n";
        $prompt .= "- Title: {$title}\n";
        $prompt .= "- Difficulty Level: {$difficulty}\n";
        $prompt .= "- Number of Questions: {$questionCount}\n";
        $prompt .= "- Question Types: " . implode(', ', $questionTypes) . "\n\n";

        $prompt .= "CONTENT GUIDELINES:\n";
        $prompt .= "- Extract key concepts and learning objectives from the document\n";
        $prompt .= "- Create questions that test comprehension and application\n";
        $prompt .= "- Maintain academic rigor appropriate for the content level\n\n";

        $prompt .= $this->getDifficultyGuidelines($difficulty);
        $prompt .= $this->getQuestionTypeGuidelines($questionTypes);
        $prompt .= $this->getOutputFormatGuidelines();

        return $prompt;
    }

    /**
     * Get difficulty-specific guidelines
     */
    private function getDifficultyGuidelines(string $difficulty): string
    {
        $guidelines = "DIFFICULTY GUIDELINES ({$difficulty}):\n";

        switch ($difficulty) {
            case 'basic':
                $guidelines .= "- Focus on recall and recognition questions\n";
                $guidelines .= "- Use simple, clear language\n";
                $guidelines .= "- Test fundamental concepts and definitions\n";
                $guidelines .= "- Provide straightforward answer choices\n";
                break;

            case 'intermediate':
                $guidelines .= "- Include application and analysis questions\n";
                $guidelines .= "- Use moderate vocabulary and complexity\n";
                $guidelines .= "- Test understanding of relationships between concepts\n";
                $guidelines .= "- Include some problem-solving scenarios\n";
                break;

            case 'advanced':
                $guidelines .= "- Focus on synthesis and evaluation questions\n";
                $guidelines .= "- Use sophisticated vocabulary and complex scenarios\n";
                $guidelines .= "- Test critical thinking and analytical skills\n";
                $guidelines .= "- Include multi-step reasoning questions\n";
                break;

            default:
                $guidelines .= "- Balance recall, application, and analysis questions\n";
                $guidelines .= "- Use appropriate vocabulary for the target audience\n";
                $guidelines .= "- Test both knowledge and understanding\n";
        }

        return $guidelines . "\n";
    }

    /**
     * Get question type specific guidelines
     */
    private function getQuestionTypeGuidelines(array $questionTypes): string
    {
        $guidelines = "QUESTION TYPE GUIDELINES:\n";

        if (in_array('mcq', $questionTypes)) {
            $guidelines .= "- Multiple Choice Questions: Provide 4 options with one clearly correct answer\n";
            $guidelines .= "- Make distractors plausible but clearly incorrect\n";
            $guidelines .= "- Avoid 'all of the above' or 'none of the above' options\n";
        }

        if (in_array('true_false', $questionTypes)) {
            $guidelines .= "- True/False Questions: Make statements clear and unambiguous\n";
            $guidelines .= "- Avoid absolute terms like 'always' or 'never' unless accurate\n";
        }

        if (in_array('short_answer', $questionTypes)) {
            $guidelines .= "- Short Answer Questions: Expect 1-3 sentence responses\n";
            $guidelines .= "- Provide clear answer guidelines and key points\n";
        }

        if (in_array('essay', $questionTypes)) {
            $guidelines .= "- Essay Questions: Require detailed explanations and analysis\n";
            $guidelines .= "- Provide clear rubric criteria for evaluation\n";
        }

        return $guidelines . "\n";
    }

    /**
     * Get output format guidelines
     */
    private function getOutputFormatGuidelines(): string
    {
        return "OUTPUT FORMAT:\n" .
               "Return the quiz in the following JSON structure:\n" .
               "{\n" .
               "  \"title\": \"Quiz Title\",\n" .
               "  \"description\": \"Brief description of the quiz\",\n" .
               "  \"questions\": [\n" .
               "    {\n" .
               "      \"question\": \"Question text\",\n" .
               "      \"type\": \"mcq|true_false|short_answer|essay\",\n" .
               "      \"options\": [\"Option 1\", \"Option 2\", \"Option 3\", \"Option 4\"], // for MCQ only\n" .
               "      \"correct_answer\": \"Correct answer or option index\",\n" .
               "      \"explanation\": \"Brief explanation of the correct answer\",\n" .
               "      \"difficulty\": \"basic|intermediate|advanced\",\n" .
               "      \"points\": 1\n" .
               "    }\n" .
               "  ]\n" .
               "}\n\n" .
               "IMPORTANT: Return ONLY the JSON structure, no additional text or formatting.\n";
    }

    /**
     * Extract content from URL
     */
    private function extractContentFromUrl(string $url): ?string
    {
        try {
            // Basic URL content extraction
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'Mozilla/5.0 (compatible; QuizBot/1.0)',
                ]
            ]);

            $content = file_get_contents($url, false, $context);

            if (!$content) {
                return null;
            }

            // Basic HTML content extraction
            $content = strip_tags($content);
            $content = preg_replace('/\s+/', ' ', $content);
            $content = trim($content);

            // Limit content length for AI processing
            return Str::limit($content, 8000);

        } catch (\Exception $e) {
            \Log::error('URL content extraction failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Extract content from uploaded document
     */
    private function extractContentFromDocument($file): ?string
    {
        try {
            $extension = strtolower($file->getClientOriginalExtension());
            $content = '';

            switch ($extension) {
                case 'txt':
                    $content = file_get_contents($file->getPathname());
                    break;

                case 'pdf':
                    // Basic PDF text extraction (would need a PDF library in production)
                    $content = $this->extractPdfContent($file);
                    break;

                case 'doc':
                case 'docx':
                    // Basic Word document extraction (would need a Word library in production)
                    $content = $this->extractWordContent($file);
                    break;

                default:
                    return null;
            }

            if (!$content) {
                return null;
            }

            // Clean and limit content
            $content = preg_replace('/\s+/', ' ', $content);
            $content = trim($content);

            return Str::limit($content, 8000);

        } catch (\Exception $e) {
            \Log::error('Document content extraction failed', [
                'file' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Extract content from PDF (placeholder - would need proper PDF library)
     */
    private function extractPdfContent($file): ?string
    {
        // Placeholder for PDF extraction
        // In production, use libraries like smalot/pdfparser or spatie/pdf-to-text
        return "PDF content extraction not implemented yet. Please use text files for now.";
    }

    /**
     * Extract content from Word document (placeholder - would need proper Word library)
     */
    private function extractWordContent($file): ?string
    {
        // Placeholder for Word extraction
        // In production, use libraries like phpoffice/phpword
        return "Word document extraction not implemented yet. Please use text files for now.";
    }

    /**
     * Create quiz from AI response
     */
    private function createQuizFromAIResponse($aiResponse, Request $request, string $sourceType): Quiz
    {
        try {
            // Parse AI response JSON
            $quizData = json_decode($aiResponse->content, true);

            if (!$quizData || !isset($quizData['questions'])) {
                throw new \Exception('Invalid AI response format');
            }

            \DB::beginTransaction();

            // Create quiz
            $quiz = Quiz::forceCreate([
                'title' => $quizData['title'] ?? $request->title ?? 'AI Generated Quiz',
                'description' => $quizData['description'] ?? 'Generated using AI from ' . $sourceType,
                'period_id' => $request->period_id ?? auth()->user()->current_period_id,
                'team_id' => auth()->user()->current_team_id,
                'user_id' => auth()->id(),
                'category_id' => $request->category_id,
                'quiz_type' => 'ai_generated',
                'creator_type' => auth()->user()->hasRole('student') ? 'student' : 'teacher',
                'status' => 'draft',
                'is_public' => $request->is_public ?? false,
                'allow_peer_sharing' => $request->allow_peer_sharing ?? true,
                'allow_result_sharing' => $request->allow_result_sharing ?? true,
                'discovery_enabled' => $request->discovery_enabled ?? false,
                'unique_code' => $this->generateUniqueCode(),
                'config' => [
                    'ai_generated' => true,
                    'source_type' => $sourceType,
                    'difficulty' => $request->difficulty ?? 'intermediate',
                    'question_count' => count($quizData['questions']),
                    'auto_grading' => true,
                    'show_results' => true,
                    'time_limit' => $request->time_limit,
                    'attempt_limit' => $request->attempt_limit ?? 0,
                ],
                'meta' => [
                    'ai_conversation_id' => $aiResponse->conversation->uuid ?? null,
                    'ai_message_id' => $aiResponse->uuid ?? null,
                    'generation_timestamp' => now(),
                ],
            ]);

            // Create questions
            foreach ($quizData['questions'] as $index => $questionData) {
                QuizQuestion::create([
                    'quiz_id' => $quiz->id,
                    'question' => $questionData['question'],
                    'question_type' => $this->mapQuestionType($questionData['type']),
                    'options' => $questionData['options'] ?? null,
                    'correct_answer' => $questionData['correct_answer'],
                    'explanation' => $questionData['explanation'] ?? null,
                    'points' => $questionData['points'] ?? 1,
                    'order_sequence' => $index + 1,
                    'config' => [
                        'difficulty' => $questionData['difficulty'] ?? 'intermediate',
                        'ai_generated' => true,
                    ],
                ]);
            }

            \DB::commit();

            return $quiz;

        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Failed to create quiz from AI response', [
                'error' => $e->getMessage(),
                'ai_response' => $aiResponse->content ?? 'No content',
            ]);
            throw new \Exception('Failed to create quiz from AI response: ' . $e->getMessage());
        }
    }

    /**
     * Map AI question types to system question types
     */
    private function mapQuestionType(string $aiType): string
    {
        $mapping = [
            'mcq' => 'multiple_choice',
            'multiple_choice' => 'multiple_choice',
            'true_false' => 'true_false',
            'short_answer' => 'short_answer',
            'essay' => 'essay',
        ];

        return $mapping[$aiType] ?? 'multiple_choice';
    }

    /**
     * Generate unique code for quiz
     */
    private function generateUniqueCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (Quiz::where('unique_code', $code)->exists());

        return $code;
    }
}
