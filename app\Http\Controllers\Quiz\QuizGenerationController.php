<?php

namespace App\Http\Controllers\Quiz;

use App\Http\Controllers\Controller;
use App\Services\AI\QuizGenerationService;
use App\Services\Quiz\QuizCategoryService;
use Illuminate\Http\Request;

class QuizGenerationController extends Controller
{
    protected QuizGenerationService $quizGenerationService;
    protected QuizCategoryService $quizCategoryService;

    public function __construct(
        QuizGenerationService $quizGenerationService,
        QuizCategoryService $quizCategoryService
    ) {
        $this->quizGenerationService = $quizGenerationService;
        $this->quizCategoryService = $quizCategoryService;
    }

    /**
     * Get prerequisites for quiz generation
     */
    public function preRequisite(Request $request)
    {
        // Check if user has quiz generation permission
        if (!auth()->user()->can('ai:generate-quiz')) {
            return response()->json(['message' => trans('user.errors.permission_denied')], 403);
        }

        $categories = $this->quizCategoryService->preRequisite($request);
        
        $difficultyLevels = [
            ['value' => 'basic', 'label' => trans('quiz.difficulty.basic')],
            ['value' => 'intermediate', 'label' => trans('quiz.difficulty.intermediate')],
            ['value' => 'advanced', 'label' => trans('quiz.difficulty.advanced')],
        ];

        $questionTypes = [
            ['value' => 'mcq', 'label' => trans('quiz.question_types.multiple_choice')],
            ['value' => 'true_false', 'label' => trans('quiz.question_types.true_false')],
            ['value' => 'short_answer', 'label' => trans('quiz.question_types.short_answer')],
            ['value' => 'essay', 'label' => trans('quiz.question_types.essay')],
        ];

        $sourceTypes = [
            ['value' => 'text', 'label' => trans('quiz.generation.source_types.text')],
            ['value' => 'topic', 'label' => trans('quiz.generation.source_types.topic')],
            ['value' => 'url', 'label' => trans('quiz.generation.source_types.url')],
            ['value' => 'document', 'label' => trans('quiz.generation.source_types.document')],
        ];

        return response()->success([
            'message' => trans('general.fetched', ['attribute' => trans('quiz.generation.prerequisites')]),
            'categories' => $categories['categories'],
            'difficultyLevels' => $difficultyLevels,
            'questionTypes' => $questionTypes,
            'sourceTypes' => $sourceTypes,
        ]);
    }

    /**
     * Generate quiz from text content
     */
    public function generateFromText(Request $request)
    {
        // Check if user has quiz generation permission
        if (!auth()->user()->can('ai:generate-quiz')) {
            return response()->json(['message' => trans('user.errors.permission_denied')], 403);
        }

        $request->validate([
            'content' => 'required|string|min:100|max:10000',
            'title' => 'nullable|string|max:255',
            'category_id' => 'nullable|uuid|exists:quiz_categories,uuid',
            'difficulty' => 'required|string|in:basic,intermediate,advanced',
            'question_count' => 'required|integer|min:1|max:50',
            'question_types' => 'required|array|min:1',
            'question_types.*' => 'string|in:mcq,true_false,short_answer,essay',
            'is_public' => 'nullable|boolean',
            'allow_peer_sharing' => 'nullable|boolean',
            'allow_result_sharing' => 'nullable|boolean',
            'time_limit' => 'nullable|integer|min:1',
            'attempt_limit' => 'nullable|integer|min:0|max:99',
        ]);

        try {
            $result = $this->quizGenerationService->generateFromText($request);

            return response()->success([
                'message' => trans('quiz.generation.success.text'),
                'quiz' => $result['quiz'],
                'conversation_id' => $result['conversation_id'],
                'source_type' => $result['source_type'],
            ]);

        } catch (\Exception $e) {
            return response()->error([
                'message' => trans('quiz.generation.error.text'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate quiz from topic/subject
     */
    public function generateFromTopic(Request $request)
    {
        // Check if user has quiz generation permission
        if (!auth()->user()->can('ai:generate-quiz')) {
            return response()->json(['message' => trans('user.errors.permission_denied')], 403);
        }

        $request->validate([
            'topic' => 'required|string|max:255',
            'subject' => 'required|string|max:100',
            'level' => 'required|string|max:50',
            'title' => 'nullable|string|max:255',
            'category_id' => 'nullable|uuid|exists:quiz_categories,uuid',
            'difficulty' => 'required|string|in:basic,intermediate,advanced',
            'question_count' => 'required|integer|min:1|max:50',
            'question_types' => 'required|array|min:1',
            'question_types.*' => 'string|in:mcq,true_false,short_answer,essay',
            'is_public' => 'nullable|boolean',
            'allow_peer_sharing' => 'nullable|boolean',
            'allow_result_sharing' => 'nullable|boolean',
            'time_limit' => 'nullable|integer|min:1',
            'attempt_limit' => 'nullable|integer|min:0|max:99',
        ]);

        try {
            $result = $this->quizGenerationService->generateFromTopic($request);

            return response()->success([
                'message' => trans('quiz.generation.success.topic'),
                'quiz' => $result['quiz'],
                'conversation_id' => $result['conversation_id'],
                'source_type' => $result['source_type'],
            ]);

        } catch (\Exception $e) {
            return response()->error([
                'message' => trans('quiz.generation.error.topic'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate quiz from URL
     */
    public function generateFromUrl(Request $request)
    {
        // Check if user has quiz generation permission
        if (!auth()->user()->can('ai:generate-quiz')) {
            return response()->json(['message' => trans('user.errors.permission_denied')], 403);
        }

        $request->validate([
            'url' => 'required|url|max:500',
            'title' => 'nullable|string|max:255',
            'category_id' => 'nullable|uuid|exists:quiz_categories,uuid',
            'difficulty' => 'required|string|in:basic,intermediate,advanced',
            'question_count' => 'required|integer|min:1|max:50',
            'question_types' => 'required|array|min:1',
            'question_types.*' => 'string|in:mcq,true_false,short_answer,essay',
            'is_public' => 'nullable|boolean',
            'allow_peer_sharing' => 'nullable|boolean',
            'allow_result_sharing' => 'nullable|boolean',
            'time_limit' => 'nullable|integer|min:1',
            'attempt_limit' => 'nullable|integer|min:0|max:99',
        ]);

        try {
            $result = $this->quizGenerationService->generateFromUrl($request);

            return response()->success([
                'message' => trans('quiz.generation.success.url'),
                'quiz' => $result['quiz'],
                'conversation_id' => $result['conversation_id'],
                'source_type' => $result['source_type'],
                'source_url' => $result['source_url'],
                'extracted_content_preview' => $result['extracted_content'],
            ]);

        } catch (\Exception $e) {
            return response()->error([
                'message' => trans('quiz.generation.error.url'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate quiz from uploaded document
     */
    public function generateFromDocument(Request $request)
    {
        // Check if user has quiz generation permission
        if (!auth()->user()->can('ai:generate-quiz')) {
            return response()->json(['message' => trans('user.errors.permission_denied')], 403);
        }

        $request->validate([
            'document' => 'required|file|mimes:txt,pdf,doc,docx|max:10240', // 10MB max
            'title' => 'nullable|string|max:255',
            'category_id' => 'nullable|uuid|exists:quiz_categories,uuid',
            'difficulty' => 'required|string|in:basic,intermediate,advanced',
            'question_count' => 'required|integer|min:1|max:50',
            'question_types' => 'required|array|min:1',
            'question_types.*' => 'string|in:mcq,true_false,short_answer,essay',
            'is_public' => 'nullable|boolean',
            'allow_peer_sharing' => 'nullable|boolean',
            'allow_result_sharing' => 'nullable|boolean',
            'time_limit' => 'nullable|integer|min:1',
            'attempt_limit' => 'nullable|integer|min:0|max:99',
        ]);

        try {
            $result = $this->quizGenerationService->generateFromDocument($request);

            return response()->success([
                'message' => trans('quiz.generation.success.document'),
                'quiz' => $result['quiz'],
                'conversation_id' => $result['conversation_id'],
                'source_type' => $result['source_type'],
                'source_file' => $result['source_file'],
                'extracted_content_preview' => $result['extracted_content'],
            ]);

        } catch (\Exception $e) {
            return response()->error([
                'message' => trans('quiz.generation.error.document'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
