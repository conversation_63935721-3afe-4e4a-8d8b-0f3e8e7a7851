<template>
    <PageHeader
        :title="
            $trans(route.meta.trans, {
                attribute: $trans(route.meta.label),
            })
        "
        :navs="[
            {
                label: $trans('quiz.quiz'),
                path: 'QuizList',
            },
            {
                label: quiz.title || $trans('quiz.quiz'),
                path: 'QuizShow',
                params: { uuid: route.params.uuid },
            },
        ]"
    >
        <PageHeaderAction
            name="Quiz"
            :title="$trans('quiz.quiz')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <div v-if="quiz.uuid" class="space-y-6">
            <!-- Overview Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <BaseCard>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ analytics.totalAttempts || 0 }}</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.total_attempts') }}</div>
                    </div>
                </BaseCard>
                
                <BaseCard>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">{{ analytics.completionRate || 0 }}%</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.completion_rate') }}</div>
                    </div>
                </BaseCard>
                
                <BaseCard>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-orange-600">{{ analytics.averageTime || 0 }}</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.avg_time_minutes') }}</div>
                    </div>
                </BaseCard>
                
                <BaseCard>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">{{ analytics.averageScore || 0 }}%</div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.average_score') }}</div>
                    </div>
                </BaseCard>
            </div>

            <!-- Score Distribution Chart -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.score_distribution') }}
                </template>
                <div class="h-64 flex items-center justify-center">
                    <!-- TODO: Implement chart component -->
                    <div class="text-gray-500">
                        <i class="fas fa-chart-bar text-4xl mb-4"></i>
                        <p>{{ $trans('quiz.chart_placeholder') }}</p>
                    </div>
                </div>
            </BaseCard>

            <!-- Question Analysis -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.question_analysis') }}
                </template>
                
                <div class="space-y-4">
                    <div 
                        v-for="(question, index) in questionAnalytics" 
                        :key="question.uuid"
                        class="border rounded-lg p-4"
                    >
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-medium">
                                {{ $trans('quiz.question') }} {{ index + 1 }}
                            </h4>
                            <div class="text-right">
                                <div class="text-lg font-bold" :class="getAccuracyColor(question.accuracy)">
                                    {{ question.accuracy || 0 }}%
                                </div>
                                <div class="text-sm text-gray-500">{{ $trans('quiz.accuracy') }}</div>
                            </div>
                        </div>
                        
                        <div class="text-sm text-gray-600 mb-3">
                            {{ question.question }}
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">{{ $trans('quiz.total_answers') }}:</span>
                                <span class="font-medium ml-1">{{ question.totalAnswers || 0 }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">{{ $trans('quiz.correct_answers') }}:</span>
                                <span class="font-medium ml-1 text-green-600">{{ question.correctAnswers || 0 }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">{{ $trans('quiz.average_time') }}:</span>
                                <span class="font-medium ml-1">{{ formatTime(question.averageTime) }}</span>
                            </div>
                        </div>

                        <!-- Option Analysis for Multiple Choice -->
                        <div v-if="question.type === 'multiple_choice' && question.optionAnalysis" class="mt-4">
                            <h5 class="font-medium mb-2">{{ $trans('quiz.option_selection') }}</h5>
                            <div class="space-y-2">
                                <div 
                                    v-for="(option, optionIndex) in question.optionAnalysis" 
                                    :key="optionIndex"
                                    class="flex items-center justify-between p-2 bg-gray-50 rounded"
                                >
                                    <span class="flex items-center">
                                        <span class="w-6 h-6 rounded-full bg-gray-300 text-white text-xs flex items-center justify-center mr-2">
                                            {{ String.fromCharCode(65 + optionIndex) }}
                                        </span>
                                        {{ option.text }}
                                        <i v-if="option.isCorrect" class="fas fa-check text-green-500 ml-2"></i>
                                    </span>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 mr-2">{{ option.count || 0 }} ({{ option.percentage || 0 }}%)</span>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div 
                                                class="h-2 rounded-full bg-blue-500"
                                                :style="{ width: `${option.percentage || 0}%` }"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </BaseCard>

            <!-- Time Analysis -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.time_analysis') }}
                </template>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium mb-3">{{ $trans('quiz.completion_times') }}</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>{{ $trans('quiz.fastest_completion') }}:</span>
                                <span class="font-medium">{{ formatTime(analytics.fastestTime) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>{{ $trans('quiz.slowest_completion') }}:</span>
                                <span class="font-medium">{{ formatTime(analytics.slowestTime) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>{{ $trans('quiz.median_time') }}:</span>
                                <span class="font-medium">{{ formatTime(analytics.medianTime) }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-medium mb-3">{{ $trans('quiz.attempt_patterns') }}</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>{{ $trans('quiz.peak_attempt_hour') }}:</span>
                                <span class="font-medium">{{ analytics.peakHour || '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>{{ $trans('quiz.most_active_day') }}:</span>
                                <span class="font-medium">{{ analytics.mostActiveDay || '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>{{ $trans('quiz.abandonment_rate') }}:</span>
                                <span class="font-medium">{{ analytics.abandonmentRate || 0 }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </BaseCard>
        </div>
    </ParentTransition>
</template>

<script>
export default {
    name: "QuizAnalytics",
}
</script>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

const quiz = reactive({})
const analytics = reactive({})
const questionAnalytics = ref([])

const fetchAnalytics = async () => {
    try {
        // TODO: Implement API calls to fetch quiz analytics
        console.log('Fetching analytics for quiz:', route.params.uuid)
    } catch (error) {
        console.error('Error fetching analytics:', error)
    }
}

const getAccuracyColor = (accuracy) => {
    if (accuracy >= 80) return 'text-green-600'
    if (accuracy >= 60) return 'text-yellow-600'
    return 'text-red-600'
}

const formatTime = (seconds) => {
    if (!seconds) return '-'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

onMounted(() => {
    fetchAnalytics()
})
</script>
