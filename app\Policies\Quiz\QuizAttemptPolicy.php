<?php

namespace App\Policies\Quiz;

use App\Models\Quiz\QuizAttempt;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class QuizAttemptPolicy
{
    use HandlesAuthorization;

    private function validateTeam(User $user, QuizAttempt $attempt)
    {
        return $attempt->quiz->team_id == $user->current_team_id;
    }

    private function validatePeriod(User $user, QuizAttempt $attempt)
    {
        return $attempt->quiz->period_id == $user->current_period_id;
    }

    /**
     * Determine whether the user can request for pre-requisites.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function preRequisite(User $user)
    {
        return $user->can('quiz:attempt');
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('quiz:view-results');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, QuizAttempt $attempt)
    {
        if (!$this->validateTeam($user, $attempt)) {
            return false;
        }

        if (!$this->validatePeriod($user, $attempt)) {
            return false;
        }

        if (!$user->can('quiz:view-results')) {
            return false;
        }

        // Students can view their own attempts
        if ($user->hasAnyRole(['student'])) {
            return $attempt->user_id === $user->id;
        }

        // Teachers and admins can view attempts for quizzes they created or have permission for
        return $attempt->quiz->user_id === $user->id || $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can create models (start attempt).
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('quiz:attempt');
    }

    /**
     * Determine whether the user can update the model (save answers).
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, QuizAttempt $attempt)
    {
        if (!$this->validateTeam($user, $attempt)) {
            return false;
        }

        if (!$this->validatePeriod($user, $attempt)) {
            return false;
        }

        if (!$user->can('quiz:attempt')) {
            return false;
        }

        // Only the user who started the attempt can update it
        if ($attempt->user_id !== $user->id) {
            return false;
        }

        // Can only update if attempt is in progress
        return $attempt->status === 'in_progress';
    }

    /**
     * Determine whether the user can submit the attempt.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function submit(User $user, QuizAttempt $attempt)
    {
        if (!$this->validateTeam($user, $attempt)) {
            return false;
        }

        if (!$this->validatePeriod($user, $attempt)) {
            return false;
        }

        if (!$user->can('quiz:attempt')) {
            return false;
        }

        // Only the user who started the attempt can submit it
        if ($attempt->user_id !== $user->id) {
            return false;
        }

        // Can only submit if attempt is in progress
        return $attempt->status === 'in_progress';
    }

    /**
     * Determine whether the user can abandon the attempt.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function abandon(User $user, QuizAttempt $attempt)
    {
        if (!$this->validateTeam($user, $attempt)) {
            return false;
        }

        if (!$this->validatePeriod($user, $attempt)) {
            return false;
        }

        if (!$user->can('quiz:attempt')) {
            return false;
        }

        // Only the user who started the attempt can abandon it
        if ($attempt->user_id !== $user->id) {
            return false;
        }

        // Can only abandon if attempt is in progress
        return $attempt->status === 'in_progress';
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, QuizAttempt $attempt)
    {
        if (!$this->validateTeam($user, $attempt)) {
            return false;
        }

        if (!$this->validatePeriod($user, $attempt)) {
            return false;
        }

        if (!$user->can('quiz:delete-attempts')) {
            return false;
        }

        // Teachers and admins can delete attempts for quizzes they created or have permission for
        return $attempt->quiz->user_id === $user->id || $user->hasAnyRole(['admin', 'super-admin']);
    }
}
