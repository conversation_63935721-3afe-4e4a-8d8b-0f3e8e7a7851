<template>
    <div class="min-h-screen bg-gray-50">
        <!-- Quiz Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div>
                        <h1 class="text-xl font-semibold text-gray-900">{{ quiz.title }}</h1>
                        <p class="text-sm text-gray-600" v-if="quiz.description">{{ quiz.description }}</p>
                    </div>
                    
                    <!-- Timer -->
                    <div v-if="quiz.timeLimit && !isCompleted" class="text-right">
                        <div class="text-2xl font-bold" :class="timeWarningClass">
                            {{ formatTime(timeRemaining) }}
                        </div>
                        <div class="text-sm text-gray-600">{{ $trans('quiz.time_remaining') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-20">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-4 text-gray-600">{{ $trans('quiz.loading_quiz') }}</p>
            </div>
        </div>

        <!-- Quiz Content -->
        <div v-else-if="!isCompleted" class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Progress Bar -->
            <div class="mb-8">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                    <span>{{ $trans('quiz.question') }} {{ currentQuestionIndex + 1 }} {{ $trans('quiz.of') }} {{ questions.length }}</span>
                    <span>{{ Math.round(((currentQuestionIndex + 1) / questions.length) * 100) }}% {{ $trans('quiz.complete') }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        :style="{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }"
                    ></div>
                </div>
            </div>

            <!-- Current Question -->
            <div v-if="currentQuestion" class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">
                        {{ currentQuestion.question }}
                    </h2>
                    
                    <!-- Question Image -->
                    <div v-if="currentQuestion.image" class="mb-4">
                        <img 
                            :src="currentQuestion.image" 
                            :alt="$trans('quiz.question_image')"
                            class="max-w-full h-auto rounded-lg"
                        />
                    </div>
                </div>

                <!-- Multiple Choice -->
                <div v-if="currentQuestion.type === 'multiple_choice'" class="space-y-3">
                    <div 
                        v-for="(option, index) in currentQuestion.options" 
                        :key="index"
                        class="flex items-center"
                    >
                        <input
                            :id="`option-${index}`"
                            :name="`question-${currentQuestion.uuid}`"
                            type="radio"
                            :value="option.uuid"
                            v-model="answers[currentQuestion.uuid]"
                            class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
                        />
                        <label 
                            :for="`option-${index}`" 
                            class="ml-3 block text-sm text-gray-700 cursor-pointer"
                        >
                            {{ option.text }}
                        </label>
                    </div>
                </div>

                <!-- True/False -->
                <div v-else-if="currentQuestion.type === 'true_false'" class="space-y-3">
                    <div class="flex items-center">
                        <input
                            id="true-option"
                            :name="`question-${currentQuestion.uuid}`"
                            type="radio"
                            value="true"
                            v-model="answers[currentQuestion.uuid]"
                            class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
                        />
                        <label for="true-option" class="ml-3 block text-sm text-gray-700 cursor-pointer">
                            {{ $trans('quiz.true') }}
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input
                            id="false-option"
                            :name="`question-${currentQuestion.uuid}`"
                            type="radio"
                            value="false"
                            v-model="answers[currentQuestion.uuid]"
                            class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
                        />
                        <label for="false-option" class="ml-3 block text-sm text-gray-700 cursor-pointer">
                            {{ $trans('quiz.false') }}
                        </label>
                    </div>
                </div>

                <!-- Short Answer -->
                <div v-else-if="currentQuestion.type === 'short_answer'">
                    <textarea
                        v-model="answers[currentQuestion.uuid]"
                        rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        :placeholder="$trans('quiz.enter_your_answer')"
                    ></textarea>
                </div>

                <!-- Essay -->
                <div v-else-if="currentQuestion.type === 'essay'">
                    <textarea
                        v-model="answers[currentQuestion.uuid]"
                        rows="6"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        :placeholder="$trans('quiz.enter_your_essay')"
                    ></textarea>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between">
                <button
                    @click="previousQuestion"
                    :disabled="currentQuestionIndex === 0"
                    class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {{ $trans('quiz.previous') }}
                </button>

                <div class="space-x-3">
                    <button
                        v-if="quiz.allowReview && currentQuestionIndex === questions.length - 1"
                        @click="showReview = true"
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                        {{ $trans('quiz.review_answers') }}
                    </button>

                    <button
                        v-if="currentQuestionIndex < questions.length - 1"
                        @click="nextQuestion"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                    >
                        {{ $trans('quiz.next') }}
                    </button>

                    <button
                        v-else
                        @click="submitQuiz"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                    >
                        {{ $trans('quiz.submit_quiz') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Review Modal -->
        <div v-if="showReview" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $trans('quiz.review_your_answers') }}</h3>
                    
                    <div class="max-h-96 overflow-y-auto space-y-4">
                        <div 
                            v-for="(question, index) in questions" 
                            :key="question.uuid"
                            class="border-b pb-4"
                        >
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-medium">{{ $trans('quiz.question') }} {{ index + 1 }}</h4>
                                <span 
                                    class="text-sm px-2 py-1 rounded"
                                    :class="answers[question.uuid] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                >
                                    {{ answers[question.uuid] ? $trans('quiz.answered') : $trans('quiz.not_answered') }}
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">{{ question.question }}</p>
                            <p class="text-sm text-gray-800">
                                <strong>{{ $trans('quiz.your_answer') }}:</strong> 
                                {{ getAnswerText(question, answers[question.uuid]) || $trans('quiz.no_answer') }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button
                            @click="showReview = false"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                            {{ $trans('quiz.continue_editing') }}
                        </button>
                        <button
                            @click="submitQuiz"
                            class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                        >
                            {{ $trans('quiz.submit_quiz') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completion State -->
        <div v-if="isCompleted" class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center">
            <div class="text-green-500 text-6xl mb-4">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 mb-4">
                {{ $trans('quiz.quiz_completed') }}
            </h2>
            <p class="text-gray-600 mb-8">
                {{ $trans('quiz.thank_you_for_taking_quiz') }}
            </p>
            
            <div v-if="result" class="bg-blue-50 p-6 rounded-lg mb-8">
                <div class="text-3xl font-bold text-blue-600 mb-2">{{ result.score }}%</div>
                <div class="text-blue-800">{{ result.message }}</div>
            </div>

            <button
                @click="viewResult"
                class="px-6 py-3 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
                {{ $trans('quiz.view_detailed_results') }}
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: "QuizPublicTake",
}
</script>

<script setup>
import { reactive, ref, computed, onMounted, onUnmounted } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const quiz = reactive({})
const questions = ref([])
const answers = reactive({})
const currentQuestionIndex = ref(0)
const showReview = ref(false)
const isCompleted = ref(false)
const result = ref(null)
const startTime = ref(null)
const timeRemaining = ref(0)
let timer = null

const currentQuestion = computed(() => {
    return questions.value[currentQuestionIndex.value]
})

const timeWarningClass = computed(() => {
    if (timeRemaining.value <= 300) return 'text-red-600' // 5 minutes
    if (timeRemaining.value <= 600) return 'text-yellow-600' // 10 minutes
    return 'text-gray-900'
})

const fetchQuizData = async () => {
    try {
        loading.value = true
        // TODO: Implement API call to fetch quiz and questions
        console.log('Fetching quiz for taking:', route.params.code)
        
        // Initialize timer if quiz has time limit
        if (quiz.timeLimit) {
            startTime.value = new Date()
            timeRemaining.value = quiz.timeLimit * 60 // Convert minutes to seconds
            startTimer()
        }
    } catch (error) {
        console.error('Error fetching quiz:', error)
    } finally {
        loading.value = false
    }
}

const startTimer = () => {
    timer = setInterval(() => {
        timeRemaining.value--
        if (timeRemaining.value <= 0) {
            submitQuiz()
        }
    }, 1000)
}

const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const nextQuestion = () => {
    if (currentQuestionIndex.value < questions.value.length - 1) {
        currentQuestionIndex.value++
    }
}

const previousQuestion = () => {
    if (currentQuestionIndex.value > 0) {
        currentQuestionIndex.value--
    }
}

const getAnswerText = (question, answer) => {
    if (!answer) return ''
    
    if (question.type === 'multiple_choice') {
        const option = question.options.find(opt => opt.uuid === answer)
        return option ? option.text : answer
    }
    
    return answer
}

const submitQuiz = async () => {
    try {
        if (timer) {
            clearInterval(timer)
        }
        
        // TODO: Implement API call to submit quiz answers
        console.log('Submitting quiz answers:', answers)
        
        isCompleted.value = true
        
        // TODO: Get result from API response
        result.value = {
            score: 85,
            message: 'Great job!'
        }
    } catch (error) {
        console.error('Error submitting quiz:', error)
    }
}

const viewResult = () => {
    // TODO: Get actual attempt UUID from submission response
    const attemptUuid = 'temp-uuid'
    router.push({
        name: 'QuizPublicResult',
        params: { 
            code: route.params.code,
            attemptUuid: attemptUuid
        }
    })
}

onMounted(() => {
    fetchQuizData()
})

onUnmounted(() => {
    if (timer) {
        clearInterval(timer)
    }
})
</script>
