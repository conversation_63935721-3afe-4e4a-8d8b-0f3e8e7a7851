import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import { useToast } from "vue-toastification"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/quiz/collections",
    formErrors: {},
    collections: [],
    currentCollection: null,
})

const collection = {
    namespaced: true,
    state: initialState,
    mutations: {
        ...mutations,
        SET_COLLECTIONS(state, collections) {
            state.collections = collections
        },
        SET_CURRENT_COLLECTION(state, collection) {
            state.currentCollection = collection
        },
        ADD_COLLECTION(state, collection) {
            state.collections.unshift(collection)
        },
        UPDATE_COLLECTION(state, updatedCollection) {
            const index = state.collections.findIndex(col => col.uuid === updatedCollection.uuid)
            if (index !== -1) {
                state.collections.splice(index, 1, updatedCollection)
            }
            if (state.currentCollection && state.currentCollection.uuid === updatedCollection.uuid) {
                state.currentCollection = updatedCollection
            }
        },
        REMOVE_COLLECTION(state, uuid) {
            state.collections = state.collections.filter(col => col.uuid !== uuid)
            if (state.currentCollection && state.currentCollection.uuid === uuid) {
                state.currentCollection = null
            }
        },
        ADD_QUIZ_TO_COLLECTION(state, { collectionUuid, quiz }) {
            const collection = state.collections.find(col => col.uuid === collectionUuid)
            if (collection) {
                if (!collection.quizzes) {
                    collection.quizzes = []
                }
                collection.quizzes.push(quiz)
                collection.quizCount = (collection.quizCount || 0) + 1
            }
            if (state.currentCollection && state.currentCollection.uuid === collectionUuid) {
                if (!state.currentCollection.quizzes) {
                    state.currentCollection.quizzes = []
                }
                state.currentCollection.quizzes.push(quiz)
                state.currentCollection.quizCount = (state.currentCollection.quizCount || 0) + 1
            }
        },
        REMOVE_QUIZ_FROM_COLLECTION(state, { collectionUuid, quizUuid }) {
            const collection = state.collections.find(col => col.uuid === collectionUuid)
            if (collection && collection.quizzes) {
                collection.quizzes = collection.quizzes.filter(quiz => quiz.uuid !== quizUuid)
                collection.quizCount = Math.max((collection.quizCount || 1) - 1, 0)
            }
            if (state.currentCollection && state.currentCollection.uuid === collectionUuid && state.currentCollection.quizzes) {
                state.currentCollection.quizzes = state.currentCollection.quizzes.filter(quiz => quiz.uuid !== quizUuid)
                state.currentCollection.quizCount = Math.max((state.currentCollection.quizCount || 1) - 1, 0)
            }
        },
    },
    actions: {
        ...actions,
        async fetch({ commit, state }, params = {}) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(state.initURL, { params })
                commit("SET_COLLECTIONS", response.collections || response.data || [])
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async get({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/${uuid}`)
                commit("SET_CURRENT_COLLECTION", response.collection)
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async create({ commit, state }, params) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(state.initURL, params)
                commit("ADD_COLLECTION", response.collection)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async update({ commit, state }, { uuid, ...params }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.patch(`${state.initURL}/${uuid}`, params)
                commit("UPDATE_COLLECTION", response.collection)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async delete({ commit, state }, uuid) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.delete(`${state.initURL}/${uuid}`)
                commit("REMOVE_COLLECTION", uuid)
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async addQuizToCollection({ commit, state }, { collectionUuid, quizUuid }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.post(`${state.initURL}/${collectionUuid}/quizzes`, { quizUuid })
                commit("ADD_QUIZ_TO_COLLECTION", { collectionUuid, quiz: response.quiz })
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async removeQuizFromCollection({ commit, state }, { collectionUuid, quizUuid }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.delete(`${state.initURL}/${collectionUuid}/quizzes/${quizUuid}`)
                commit("REMOVE_QUIZ_FROM_COLLECTION", { collectionUuid, quizUuid })
                commit("SET_LOADING", false)
                toast.success(response.message)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getMyCollections({ commit, state }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/my`)
                commit("SET_COLLECTIONS", response.collections || [])
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getPublicCollections({ commit, state }, params = {}) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/public`, { params })
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
        async getCollectionQuizzes({ commit, state }, { uuid, params = {} }) {
            try {
                commit("SET_LOADING", true)
                const response = await Api.get(`${state.initURL}/${uuid}/quizzes`, { params })
                commit("SET_LOADING", false)
                return response
            } catch (error) {
                commit("SET_LOADING", false)
                Form.handleErrors(error)
                throw error
            }
        },
    },
    getters: {
        ...getters,
        collections: (state) => state.collections,
        currentCollection: (state) => state.currentCollection,
        getOptions: (state) => () => {
            return state.collections.map(collection => ({
                value: collection.uuid,
                label: collection.name,
            }))
        },
        getCollectionByUuid: (state) => (uuid) => {
            return state.collections.find(collection => collection.uuid === uuid)
        },
        publicCollectionsCount: (state) => {
            return state.collections.filter(collection => collection.isPublic).length
        },
        myCollectionsCount: (state) => {
            return state.collections.filter(collection => !collection.isPublic).length
        },
    },
}

export default collection
