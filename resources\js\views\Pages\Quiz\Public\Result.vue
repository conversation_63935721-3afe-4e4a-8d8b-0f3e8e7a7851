<template>
    <div class="min-h-screen bg-gray-50 py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    {{ $trans('quiz.quiz_results') }}
                </h1>
                <p class="text-gray-600" v-if="quiz.title">{{ quiz.title }}</p>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="text-center py-20">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-4 text-gray-600">{{ $trans('quiz.loading_results') }}</p>
            </div>

            <!-- Results Content -->
            <div v-else-if="attempt.uuid" class="space-y-8">
                <!-- Score Overview -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="text-center">
                        <div class="text-6xl font-bold mb-4" :class="getScoreColor(attempt.score)">
                            {{ attempt.score }}%
                        </div>
                        <div class="text-xl text-gray-700 mb-2">
                            {{ getScoreMessage(attempt.score) }}
                        </div>
                        <div class="text-gray-600">
                            {{ attempt.correctAnswers }} {{ $trans('quiz.out_of') }} {{ attempt.totalQuestions }} {{ $trans('quiz.questions_correct') }}
                        </div>
                    </div>

                    <!-- Additional Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8 pt-6 border-t">
                        <div class="text-center">
                            <div class="text-2xl font-semibold text-gray-900">{{ formatTime(attempt.timeSpent) }}</div>
                            <div class="text-sm text-gray-600">{{ $trans('quiz.time_spent') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-semibold text-gray-900">{{ attempt.attemptNumber }}</div>
                            <div class="text-sm text-gray-600">{{ $trans('quiz.attempt_number') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-semibold" :class="attempt.passed ? 'text-green-600' : 'text-red-600'">
                                {{ attempt.passed ? $trans('quiz.passed') : $trans('quiz.failed') }}
                            </div>
                            <div class="text-sm text-gray-600">{{ $trans('quiz.status') }}</div>
                        </div>
                    </div>
                </div>

                <!-- Participant Information -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $trans('quiz.participant_information') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <span class="text-gray-600">{{ $trans('quiz.participant.name') }}:</span>
                            <span class="font-medium ml-2">{{ attempt.participant?.name }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">{{ $trans('quiz.participant.email') }}:</span>
                            <span class="font-medium ml-2">{{ attempt.participant?.email }}</span>
                        </div>
                        <div v-if="attempt.participant?.organization">
                            <span class="text-gray-600">{{ $trans('quiz.participant.organization') }}:</span>
                            <span class="font-medium ml-2">{{ attempt.participant.organization }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">{{ $trans('quiz.completed_at') }}:</span>
                            <span class="font-medium ml-2">{{ formatDate(attempt.completedAt) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Question Results -->
                <div v-if="quiz.showCorrectAnswers && questionResults.length > 0" class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">{{ $trans('quiz.question_by_question_results') }}</h3>
                    
                    <div class="space-y-6">
                        <div 
                            v-for="(questionResult, index) in questionResults" 
                            :key="questionResult.uuid"
                            class="border-b pb-6 last:border-b-0"
                        >
                            <!-- Question Header -->
                            <div class="flex justify-between items-start mb-4">
                                <h4 class="font-medium text-gray-900">
                                    {{ $trans('quiz.question') }} {{ index + 1 }}
                                </h4>
                                <span 
                                    class="px-3 py-1 rounded-full text-sm font-medium"
                                    :class="questionResult.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                >
                                    {{ questionResult.isCorrect ? $trans('quiz.correct') : $trans('quiz.incorrect') }}
                                </span>
                            </div>

                            <!-- Question Text -->
                            <div class="mb-4">
                                <p class="text-gray-700">{{ questionResult.question }}</p>
                                <div v-if="questionResult.image" class="mt-2">
                                    <img 
                                        :src="questionResult.image" 
                                        :alt="$trans('quiz.question_image')"
                                        class="max-w-sm h-auto rounded-lg"
                                    />
                                </div>
                            </div>

                            <!-- Multiple Choice Results -->
                            <div v-if="questionResult.type === 'multiple_choice'" class="space-y-2">
                                <div 
                                    v-for="option in questionResult.options" 
                                    :key="option.uuid"
                                    class="flex items-center p-2 rounded"
                                    :class="getOptionClass(option, questionResult)"
                                >
                                    <div class="flex items-center">
                                        <div 
                                            class="w-4 h-4 rounded-full border-2 mr-3"
                                            :class="option.uuid === questionResult.userAnswer ? 'border-blue-500 bg-blue-500' : 'border-gray-300'"
                                        ></div>
                                        <span>{{ option.text }}</span>
                                    </div>
                                    <div class="ml-auto flex items-center space-x-2">
                                        <span v-if="option.isCorrect" class="text-green-600">
                                            <i class="fas fa-check"></i> {{ $trans('quiz.correct_answer') }}
                                        </span>
                                        <span v-if="option.uuid === questionResult.userAnswer" class="text-blue-600">
                                            <i class="fas fa-user"></i> {{ $trans('quiz.your_answer') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- True/False Results -->
                            <div v-else-if="questionResult.type === 'true_false'" class="space-y-2">
                                <div class="grid grid-cols-2 gap-4">
                                    <div 
                                        class="p-3 rounded border"
                                        :class="questionResult.correctAnswer === 'true' ? 'border-green-500 bg-green-50' : 'border-gray-300'"
                                    >
                                        <div class="flex items-center justify-between">
                                            <span>{{ $trans('quiz.true') }}</span>
                                            <div class="flex items-center space-x-2">
                                                <span v-if="questionResult.correctAnswer === 'true'" class="text-green-600">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                <span v-if="questionResult.userAnswer === 'true'" class="text-blue-600">
                                                    <i class="fas fa-user"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div 
                                        class="p-3 rounded border"
                                        :class="questionResult.correctAnswer === 'false' ? 'border-green-500 bg-green-50' : 'border-gray-300'"
                                    >
                                        <div class="flex items-center justify-between">
                                            <span>{{ $trans('quiz.false') }}</span>
                                            <div class="flex items-center space-x-2">
                                                <span v-if="questionResult.correctAnswer === 'false'" class="text-green-600">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                <span v-if="questionResult.userAnswer === 'false'" class="text-blue-600">
                                                    <i class="fas fa-user"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Text Answer Results -->
                            <div v-else class="space-y-3">
                                <div>
                                    <h5 class="font-medium text-gray-700 mb-2">{{ $trans('quiz.your_answer') }}:</h5>
                                    <div class="p-3 bg-blue-50 rounded border">
                                        {{ questionResult.userAnswer || $trans('quiz.no_answer_provided') }}
                                    </div>
                                </div>
                                <div v-if="questionResult.correctAnswer">
                                    <h5 class="font-medium text-gray-700 mb-2">{{ $trans('quiz.correct_answer') }}:</h5>
                                    <div class="p-3 bg-green-50 rounded border">
                                        {{ questionResult.correctAnswer }}
                                    </div>
                                </div>
                                <div v-if="questionResult.explanation">
                                    <h5 class="font-medium text-gray-700 mb-2">{{ $trans('quiz.explanation') }}:</h5>
                                    <div class="p-3 bg-gray-50 rounded border">
                                        {{ questionResult.explanation }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="text-center space-y-4">
                    <div class="space-x-4">
                        <button
                            @click="printResults"
                            class="px-6 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                            <i class="fas fa-print mr-2"></i>
                            {{ $trans('quiz.print_results') }}
                        </button>
                        
                        <button
                            v-if="canRetake"
                            @click="retakeQuiz"
                            class="px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                        >
                            {{ $trans('quiz.retake_quiz') }}
                        </button>
                    </div>
                    
                    <div>
                        <button
                            @click="goToQuizAccess"
                            class="text-sm text-blue-600 hover:text-blue-800"
                        >
                            {{ $trans('quiz.back_to_quiz') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Not Found -->
            <div v-else class="text-center py-20">
                <div class="text-red-500 text-6xl mb-4">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                    {{ $trans('quiz.results_not_found') }}
                </h3>
                <p class="text-gray-600">
                    {{ $trans('quiz.invalid_result_link') }}
                </p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "QuizPublicResult",
}
</script>

<script setup>
import { reactive, ref, computed, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const quiz = reactive({})
const attempt = reactive({})
const questionResults = ref([])

const canRetake = computed(() => {
    return !quiz.maxAttempts || attempt.attemptNumber < quiz.maxAttempts
})

const fetchResults = async () => {
    try {
        loading.value = true
        // TODO: Implement API call to fetch quiz results
        console.log('Fetching results for:', route.params.code, route.params.attemptUuid)
    } catch (error) {
        console.error('Error fetching results:', error)
    } finally {
        loading.value = false
    }
}

const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
}

const getScoreMessage = (score) => {
    if (score >= 90) return 'Excellent!'
    if (score >= 80) return 'Great job!'
    if (score >= 70) return 'Good work!'
    if (score >= 60) return 'Not bad!'
    return 'Keep trying!'
}

const getOptionClass = (option, questionResult) => {
    if (option.isCorrect) {
        return 'bg-green-50 border-green-200'
    }
    if (option.uuid === questionResult.userAnswer && !option.isCorrect) {
        return 'bg-red-50 border-red-200'
    }
    return 'bg-gray-50 border-gray-200'
}

const formatTime = (seconds) => {
    if (!seconds) return '0:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatDate = (date) => {
    if (!date) return ''
    return new Date(date).toLocaleString()
}

const printResults = () => {
    window.print()
}

const retakeQuiz = () => {
    router.push({
        name: 'QuizPublicAccess',
        params: { code: route.params.code }
    })
}

const goToQuizAccess = () => {
    router.push({
        name: 'QuizPublicAccess',
        params: { code: route.params.code }
    })
}

onMounted(() => {
    fetchResults()
})
</script>
